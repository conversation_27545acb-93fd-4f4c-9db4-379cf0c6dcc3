# MindMapGen 插件优化总结

## 🎯 优化目标

参考WeatherReporter等其他VCP插件的标准写法，对MindMapGen插件进行全面优化，确保：
- 配置文件优先级处理（插件配置 > 主服务器配置 > 默认值）
- 标准化的工具调用格式
- 统一的日志系统
- 完善的错误处理

## ✅ 主要优化内容

### 1. 配置文件优先级处理

**优化前**:
```javascript
this.config = {
    apiUrl: process.env.OPENAI_API_URL || 'https://api.openai.com',
    apiKey: process.env.OPENAI_API_KEY,
    // ...
};
```

**优化后**:
```javascript
// 配置优先级：插件配置 > 主服务器环境变量 > 默认值
const mergedConfig = {
    OPENAI_API_URL: pluginConfig.OPENAI_API_URL || process.env.OPENAI_API_URL || defaultConfig.OPENAI_API_URL,
    OPENAI_API_KEY: pluginConfig.OPENAI_API_KEY || process.env.OPENAI_API_KEY || '',
    // ...
};
```

### 2. 标准化工具调用格式

添加了WeatherReporter风格的工具调用格式：

```text
<<<[TOOL_REQUEST]>>>
tool_name:「始」MindMapGen「末」,
prompt:「始」思维导图内容描述「末」,
width:「始」(可选, 默认2400) 输出图片宽度（像素）「末」,
height:「始」(可选, 默认1600) 输出图片高度（像素）「末」,
style:「始」(可选, 默认default) 样式主题「末」,
waitTime:「始」(可选, 默认8000) 渲染等待时间（毫秒）「末」
<<<[END_TOOL_REQUEST]>>>
```

### 3. 统一日志系统

**优化前**:
```javascript
console.log('开始生成思维导图:', options);
console.error('AI生成失败:', error.message);
```

**优化后**:
```javascript
logger.info('思维导图生成', `开始生成思维导图: ${options.prompt.substring(0, 50)}...`);
logger.error('思维导图生成', 'AI生成失败:', error.message);
logger.debug('思维导图生成', `AI生成成功，内容长度: ${content.length}`);
```

### 4. 配置验证和调试模式

添加了配置验证和调试模式支持：

```javascript
// 验证必需配置
if (!this.config.apiKey) {
    logger.error('思维导图生成', 'OPENAI_API_KEY未配置，插件可能无法正常工作');
}

// 调试模式
if (this.config.debugMode) {
    logger.debug('思维导图生成', '插件配置:', this.config);
}
```

## 📁 配置文件优化

### config.env.example 更新

```env
# 思维导图生成器配置文件示例
# 复制此文件为 config.env 并填入实际配置

# OpenAI API 配置
OPENAI_API_URL=https://api.openai.com
OPENAI_API_KEY=your_openai_api_key_here

# 思维导图生成配置
MINDMAP_MODEL=gpt-4o-mini
MINDMAP_TIMEOUT=30000

# 项目路径配置（图片将保存到 PROJECT_BASE_PATH/image/mindmapgen/ 目录）
PROJECT_BASE_PATH=../../

# 输出配置
MINDMAP_WIDTH=2400
MINDMAP_HEIGHT=1600
MINDMAP_WAIT_TIME=8000

# 可选：使用其他兼容的API服务
# OPENAI_API_URL=https://api.deepseek.com
# OPENAI_API_KEY=your_deepseek_api_key

# 可选：使用本地模型服务
# OPENAI_API_URL=http://localhost:11434/v1
# OPENAI_API_KEY=ollama

# 调试模式 (true/false)
DebugMode=false
```

### plugin-manifest.json 更新

添加了标准化的工具调用格式说明：

```json
{
  "capabilities": {
    "invocationCommands": [
      {
        "commandIdentifier": "MindMapGen",
        "description": "调用此工具生成AI思维导图...",
        "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MindMapGen「末」,\nprompt:「始」人工智能的发展历程和应用领域「末」\n<<<[END_TOOL_REQUEST]>>>\n```"
      }
    ]
  }
}
```

## 🔧 技术改进

### 1. 配置加载函数

```javascript
function loadPluginConfig() {
    const pluginConfigPath = path.join(__dirname, 'config.env');
    let pluginConfig = {};

    // 加载插件配置
    try {
        const configContent = require('fs').readFileSync(pluginConfigPath, 'utf8');
        pluginConfig = parseEnvConfig(configContent);
        logger.info('思维导图生成', `从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
    } catch (error) {
        logger.info('思维导图生成', `插件config.env不存在或读取失败，使用主服务器配置作为备用`);
    }

    // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
    // ...
}
```

### 2. 日志系统集成

```javascript
// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    logger = vcpLogger;
} catch (error) {
    // 如果日志系统不可用，使用console作为备用
    logger = {
        info: (tag, message, data) => console.log(`[${tag}] ${message}`, data || ''),
        error: (tag, message, data) => console.error(`[${tag}] ${message}`, data || ''),
        // ...
    };
}
```

### 3. 环境配置解析

```javascript
function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                config[key] = value;
            }
        }
    });
    return config;
}
```

## 🚀 使用方式

### 工具调用格式（推荐）

```text
<<<[TOOL_REQUEST]>>>
tool_name:「始」MindMapGen「末」,
prompt:「始」人工智能的发展历程：机器学习、深度学习、自然语言处理等主要分支「末」,
style:「始」colorful「末」
<<<[END_TOOL_REQUEST]>>>
```

### 直接调用格式

```
{{MindMapGen}}
参数：{
  "prompt": "人工智能的发展历程：机器学习、深度学习、自然语言处理等主要分支",
  "style": "colorful"
}
```

## 📊 配置优先级示例

假设有以下配置：

1. **插件config.env**: `MINDMAP_MODEL=gpt-4`
2. **主服务器环境变量**: `MINDMAP_MODEL=gpt-3.5-turbo`
3. **默认值**: `MINDMAP_MODEL=gpt-4o-mini`

**最终使用**: `gpt-4` (插件配置优先)

## 🔍 调试功能

启用调试模式：
```env
DebugMode=true
```

调试输出示例：
```
[DEBUG][思维导图生成] 插件配置: { apiUrl: 'https://api.openai.com', model: 'gpt-4o-mini', ... }
[DEBUG][思维导图生成] 输出目录已确保存在: /path/to/image/mindmapgen
[DEBUG][思维导图生成] 调用AI API: https://api.openai.com, 模型: gpt-4o-mini
[DEBUG][思维导图生成] AI生成成功，内容长度: 1234
```

## 🎉 优化成果

1. ✅ **配置管理**: 实现了三级配置优先级
2. ✅ **工具调用**: 添加了标准化的工具调用格式
3. ✅ **日志系统**: 集成了VCP统一日志系统
4. ✅ **错误处理**: 完善了错误处理和调试功能
5. ✅ **文档更新**: 更新了所有相关文档

现在MindMapGen插件完全符合VCP插件的标准规范，与WeatherReporter等插件保持一致的实现风格！
