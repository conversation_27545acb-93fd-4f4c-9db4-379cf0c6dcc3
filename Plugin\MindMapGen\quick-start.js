/**
 * MindMapGen 快速启动脚本
 * 提供交互式的思维导图生成体验
 */

const MindMapGenVCP = require('./MindMapGen');
const readline = require('readline');
const path = require('path');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, 'config.env') });

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function interactiveGeneration() {
    console.log('🎨 MindMapGen 交互式思维导图生成器\n');
    
    const plugin = new MindMapGenVCP();
    
    while (true) {
        console.log('请选择操作：');
        console.log('1. 生成思维导图');
        console.log('2. 查看样式选项');
        console.log('3. 查看示例提示词');
        console.log('4. 退出');
        
        const choice = await question('\n请输入选项 (1-4): ');
        
        switch (choice.trim()) {
            case '1':
                await generateMindMap(plugin);
                break;
            case '2':
                showStyleOptions();
                break;
            case '3':
                showExamplePrompts();
                break;
            case '4':
                console.log('👋 再见！');
                rl.close();
                return;
            default:
                console.log('❌ 无效选项，请重新选择\n');
        }
    }
}

async function generateMindMap(plugin) {
    console.log('\n📝 生成思维导图\n');
    
    try {
        // 获取用户输入
        const prompt = await question('请输入思维导图主题描述: ');
        if (!prompt.trim()) {
            console.log('❌ 主题描述不能为空\n');
            return;
        }
        
        const style = await question('选择样式 (default/colorful/dark/minimal) [default]: ') || 'default';
        const widthInput = await question('图片宽度 [2400]: ') || '2400';
        const heightInput = await question('图片高度 [1600]: ') || '1600';
        
        const width = parseInt(widthInput);
        const height = parseInt(heightInput);
        
        if (isNaN(width) || isNaN(height)) {
            console.log('❌ 宽度和高度必须是数字\n');
            return;
        }
        
        console.log('\n🚀 开始生成思维导图...');
        console.log('⏳ 这可能需要30-60秒，请耐心等待...\n');
        
        const startTime = Date.now();
        
        const args = {
            prompt: prompt.trim(),
            style: style.trim(),
            width: width,
            height: height
        };
        
        const result = await plugin.execute(JSON.stringify(args));
        const parsedResult = JSON.parse(result);
        
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        if (parsedResult.status === 'success') {
            console.log('✅ 思维导图生成成功！');
            console.log(`⏱️  耗时: ${duration}秒`);
            console.log(`📁 文件路径: ${parsedResult.data.image_path}`);
            console.log(`📏 文件大小: ${formatFileSize(parsedResult.data.generation_info.file_size)}`);
            console.log(`🎨 样式: ${parsedResult.data.style}`);
            console.log(`📊 尺寸: ${parsedResult.data.width} x ${parsedResult.data.height}`);
            
            // 显示生成的Markdown内容预览
            if (parsedResult.data.markdown_content) {
                console.log('\n📋 生成的思维导图结构预览:');
                const lines = parsedResult.data.markdown_content.split('\n').slice(0, 10);
                lines.forEach(line => {
                    if (line.trim()) {
                        console.log(`  ${line}`);
                    }
                });
                if (parsedResult.data.markdown_content.split('\n').length > 10) {
                    console.log('  ...');
                }
            }
        } else {
            console.log('❌ 思维导图生成失败');
            console.log(`🔍 错误信息: ${parsedResult.message}`);
        }
        
    } catch (error) {
        console.log('💥 生成过程中发生错误:', error.message);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
}

function showStyleOptions() {
    console.log('\n🎨 可用样式选项:\n');
    
    const styles = [
        {
            name: 'default',
            description: '经典白色背景，适合正式文档'
        },
        {
            name: 'colorful',
            description: '彩色渐变背景，视觉效果丰富'
        },
        {
            name: 'dark',
            description: '深色主题，适合暗色环境'
        },
        {
            name: 'minimal',
            description: '简约风格，突出内容结构'
        }
    ];
    
    styles.forEach((style, index) => {
        console.log(`${index + 1}. ${style.name}`);
        console.log(`   ${style.description}\n`);
    });
}

function showExamplePrompts() {
    console.log('\n💡 示例提示词:\n');
    
    const examples = [
        {
            category: '学习笔记',
            prompt: '机器学习的基本概念：监督学习、无监督学习、强化学习的区别和应用场景'
        },
        {
            category: '项目规划',
            prompt: '开发一个电商网站的完整流程：需求分析、技术选型、架构设计、开发实施、测试部署'
        },
        {
            category: '知识体系',
            prompt: '现代前端开发技术栈：HTML5、CSS3、JavaScript ES6+、React/Vue、构建工具、测试框架'
        },
        {
            category: '业务分析',
            prompt: '客户服务流程优化：问题接收、分类处理、专家分配、解决方案、客户反馈、质量评估'
        },
        {
            category: '技术架构',
            prompt: '微服务架构设计：服务拆分原则、API网关、服务发现、配置中心、监控告警、容器化部署'
        }
    ];
    
    examples.forEach((example, index) => {
        console.log(`${index + 1}. ${example.category}`);
        console.log(`   "${example.prompt}"\n`);
    });
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function checkEnvironment() {
    if (!process.env.OPENAI_API_KEY) {
        console.log('❌ 错误：未配置 OPENAI_API_KEY');
        console.log('💡 请编辑 config.env 文件，填入您的API密钥');
        process.exit(1);
    }
    
    console.log('✅ 环境配置检查通过');
}

async function main() {
    try {
        checkEnvironment();
        await interactiveGeneration();
    } catch (error) {
        console.error('💥 程序运行错误:', error.message);
        rl.close();
        process.exit(1);
    }
}

// 如果直接运行此文件，则启动交互式程序
if (require.main === module) {
    main();
}

module.exports = {
    interactiveGeneration,
    generateMindMap,
    showStyleOptions,
    showExamplePrompts
};
