/**
 * MindMapGen 插件安装脚本
 * 自动安装依赖并检查环境配置
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 MindMapGen 插件安装程序\n');

async function checkNodeVersion() {
    console.log('📋 检查Node.js版本...');
    
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 16) {
        console.log(`❌ Node.js版本过低: ${nodeVersion}`);
        console.log('💡 请升级到Node.js 16.0.0或更高版本');
        process.exit(1);
    }
    
    console.log(`✅ Node.js版本: ${nodeVersion}\n`);
}

async function installDependencies() {
    console.log('📦 安装依赖包...');
    
    return new Promise((resolve, reject) => {
        const npm = spawn('npm', ['install'], {
            stdio: 'inherit',
            cwd: __dirname
        });
        
        npm.on('close', (code) => {
            if (code === 0) {
                console.log('✅ 依赖安装完成\n');
                resolve();
            } else {
                console.log('❌ 依赖安装失败');
                reject(new Error(`npm install failed with code ${code}`));
            }
        });
        
        npm.on('error', (error) => {
            console.log('❌ 启动npm失败:', error.message);
            reject(error);
        });
    });
}

async function setupConfig() {
    console.log('⚙️  配置环境文件...');
    
    const configPath = path.join(__dirname, 'config.env');
    const examplePath = path.join(__dirname, 'config.env.example');
    
    if (!fs.existsSync(configPath)) {
        if (fs.existsSync(examplePath)) {
            fs.copyFileSync(examplePath, configPath);
            console.log('✅ 已创建 config.env 文件');
            console.log('⚠️  请编辑 config.env 文件，填入您的API密钥');
        } else {
            console.log('❌ 找不到 config.env.example 文件');
        }
    } else {
        console.log('ℹ️  config.env 文件已存在');
    }
    
    console.log('');
}

async function createOutputDirectory() {
    console.log('📁 创建输出目录...');
    
    const outputDir = path.join(__dirname, '../../resources/mindmaps');
    
    try {
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
            console.log(`✅ 已创建输出目录: ${outputDir}`);
        } else {
            console.log(`ℹ️  输出目录已存在: ${outputDir}`);
        }
    } catch (error) {
        console.log(`❌ 创建输出目录失败: ${error.message}`);
    }
    
    console.log('');
}

async function checkPuppeteerInstallation() {
    console.log('🌐 检查Puppeteer安装...');
    
    try {
        const puppeteer = require('puppeteer');
        console.log('✅ Puppeteer已安装');
        
        // 检查Chrome是否可用
        try {
            const browser = await puppeteer.launch({ headless: 'new' });
            await browser.close();
            console.log('✅ Chrome浏览器可用');
        } catch (error) {
            console.log('⚠️  Chrome浏览器测试失败:', error.message);
            console.log('💡 可能需要手动安装Chrome或设置环境变量');
        }
    } catch (error) {
        console.log('❌ Puppeteer未安装或安装失败');
        console.log('💡 请运行: npm install puppeteer');
    }
    
    console.log('');
}

async function runTest() {
    console.log('🧪 运行基础测试...');
    
    const configPath = path.join(__dirname, 'config.env');
    
    if (!fs.existsSync(configPath)) {
        console.log('⚠️  跳过测试：config.env文件不存在');
        return;
    }
    
    // 检查是否配置了API密钥
    const configContent = fs.readFileSync(configPath, 'utf8');
    if (!configContent.includes('OPENAI_API_KEY=') || configContent.includes('your_openai_api_key_here')) {
        console.log('⚠️  跳过测试：请先配置API密钥');
        return;
    }
    
    try {
        const testModule = require('./test');
        await testModule.checkEnvironment();
        console.log('✅ 环境检查通过');
    } catch (error) {
        console.log('❌ 环境检查失败:', error.message);
    }
    
    console.log('');
}

function printNextSteps() {
    console.log('🎉 安装完成！\n');
    console.log('📋 下一步操作：');
    console.log('1. 编辑 config.env 文件，填入您的OpenAI API密钥');
    console.log('2. 运行测试: node test.js');
    console.log('3. 在VCP系统中使用: {{MindMapGen}}');
    console.log('4. 查看文档: README.md 和 USAGE_EXAMPLES.md\n');
    
    console.log('💡 示例用法：');
    console.log('{{MindMapGen}}');
    console.log('参数：{"prompt": "人工智能的发展历程和应用领域"}');
    console.log('');
}

async function main() {
    try {
        await checkNodeVersion();
        await installDependencies();
        await setupConfig();
        await createOutputDirectory();
        await checkPuppeteerInstallation();
        await runTest();
        printNextSteps();
    } catch (error) {
        console.error('💥 安装过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行安装
if (require.main === module) {
    main();
}

module.exports = {
    checkNodeVersion,
    installDependencies,
    setupConfig,
    createOutputDirectory,
    checkPuppeteerInstallation,
    runTest
};
