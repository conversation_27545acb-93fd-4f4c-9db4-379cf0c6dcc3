{"name": "NovelAIGen", "version": "1.0.0", "description": "使用NovelAI/Noobai模型生成动漫风格图片，支持角色外貌配置", "displayName": "NovelAIGen 动漫图片生成", "main": "NovelAIGen.js", "pluginType": "synchronous", "type": "image_generation", "author": "VCPToolBox", "dependencies": ["axios"], "entryPoint": {"command": "node NovelAIGen.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "configSchema": {"YUANPLUS_API_KEY": {"type": "string", "description": "YuanPlus云平台API密钥（留空则继承主配置）", "required": false, "inherit_from": "YUANPLUS_API_KEY"}, "ENABLE_CHARACTER_APPEARANCE": {"type": "boolean", "description": "是否启用角色外貌特征自动应用", "required": false, "default": true}, "DEFAULT_CHARACTER_APPEARANCE": {"type": "string", "description": "默认角色外貌特征（如果全局配置中没有CharAppearance时使用）", "required": false, "default": "best quality, ultra-detailed, absurdres, 1girl, anime style"}, "角色名_CHARACTER_APPEARANCE": {"type": "string", "description": "特定角色的外貌配置，格式：角色名_CHARACTER_APPEARANCE=外貌描述", "required": false, "example": "ALICE_CHARACTER_APPEARANCE=1girl, blonde hair, blue eyes, school uniform", "pattern": "^[A-Za-z0-9_]+_CHARACTER_APPEARANCE$"}}, "parameters": {"prompt": {"type": "string", "description": "场景描述，如'在咖啡厅拍照'、'海边拍照'等", "required": true}, "model": {"type": "string", "description": "使用的模型", "default": "luminaArchitect-v1-fast", "enum": ["luminaArchitect-v1-fast", "luminaArchitect-v1-turbo", "luminaArchitect-v1-plus", "noobai-fast", "kusa-image-generator", "wai-illustrious-free", "anishadow-v10-free"]}, "include_character": {"type": "boolean", "description": "是否明确需要画角色", "required": false, "default": false}, "character_names": {"type": "array", "items": {"type": "string"}, "description": "要画的角色名称数组，按照优先级排列。系统会查找对应的角色外貌配置（格式：角色名_CHARACTER_APPEARANCE）", "required": false, "default": []}}}