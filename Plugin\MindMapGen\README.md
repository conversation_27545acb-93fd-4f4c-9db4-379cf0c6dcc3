# MindMapGen 思维导图生成器

## 📋 概述

MindMapGen 是一个基于AI的思维导图生成插件，支持VCP和MCP两种协议。它能够根据用户的描述自动生成Markmap格式的思维导图，并渲染为高质量的PNG图片。

## ✨ 主要功能

- 🤖 **AI智能生成**：基于OpenAI API生成结构化的思维导图内容
- 🎨 **多样式主题**：支持default、colorful、dark、minimal四种样式
- 📏 **自定义尺寸**：可自定义输出图片的宽度和高度
- 🖼️ **高质量渲染**：使用Puppeteer渲染为PNG图片
- 📁 **本地存储**：生成的图片保存在本地，支持绝对路径访问
- 🔧 **灵活配置**：支持多种配置选项和环境变量

## 🚀 快速开始

### 1. 安装依赖

```bash
cd Plugin/MindMapGen
npm install
```

### 2. 配置环境

复制配置文件并填入API密钥：

```bash
cp config.env.example config.env
```

编辑 `config.env` 文件：

```env
OPENAI_API_KEY=your_openai_api_key_here
PROJECT_BASE_PATH=../../
```

### 3. VCP工具调用格式

```text
<<<[TOOL_REQUEST]>>>
tool_name:「始」MindMapGen「末」,
prompt:「始」人工智能的发展历程和应用领域「末」,
style:「始」colorful「末」
<<<[END_TOOL_REQUEST]>>>
```

### 4. VCP直接调用示例

```
{{MindMapGen}}
参数：{
  "prompt": "人工智能的发展历程和应用领域"
}
```

### 5. MCP调用示例

```json
{
  "method": "tools/call",
  "params": {
    "name": "MindMapGen",
    "arguments": {
      "prompt": "项目管理的核心要素和流程",
      "width": 2400,
      "height": 1600,
      "style": "colorful"
    }
  }
}
```

## 📖 详细使用说明

### 参数说明

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| prompt | string | ✅ | - | 思维导图内容描述 |
| width | number | ❌ | 2400 | 图片宽度（800-4000） |
| height | number | ❌ | 1600 | 图片高度（600-3000） |
| waitTime | number | ❌ | 8000 | 渲染等待时间（毫秒） |
| style | string | ❌ | default | 样式主题 |

### 样式主题

- **default**：经典白色背景，适合正式文档
- **colorful**：彩色渐变背景，视觉效果丰富
- **dark**：深色主题，适合暗色环境
- **minimal**：简约风格，突出内容结构

### 使用场景

1. **学习笔记**：整理知识点和概念关系
2. **项目规划**：梳理项目结构和任务分解
3. **会议记录**：可视化会议要点和决策
4. **知识管理**：构建知识体系和思维框架
5. **教学辅助**：制作教学思维导图

## 🔧 配置选项

### 环境变量

```env
# API配置
OPENAI_API_URL=https://api.openai.com
OPENAI_API_KEY=your_api_key
MINDMAP_MODEL=gpt-4o-mini
MINDMAP_TIMEOUT=30000

# 项目路径配置（图片将保存到 PROJECT_BASE_PATH/image/mindmapgen/ 目录）
PROJECT_BASE_PATH=../../

# 输出配置
MINDMAP_WIDTH=2400
MINDMAP_HEIGHT=1600
MINDMAP_WAIT_TIME=8000
```

### 支持的API服务

除了OpenAI官方API，还支持兼容的第三方服务：

```env
# DeepSeek API
OPENAI_API_URL=https://api.deepseek.com
OPENAI_API_KEY=your_deepseek_key

# 本地Ollama服务
OPENAI_API_URL=http://localhost:11434/v1
OPENAI_API_KEY=ollama
```

## 📝 使用示例

### 基础使用

```json
{
  "prompt": "机器学习的基本概念和算法分类"
}
```

### 自定义样式和尺寸

```json
{
  "prompt": "软件开发生命周期",
  "width": 3200,
  "height": 2400,
  "style": "dark"
}
```

### 复杂主题

```json
{
  "prompt": "构建一个完整的电商系统架构，包括前端、后端、数据库、缓存、消息队列等组件"
}
```

## 📊 返回格式

### VCP插件返回格式

VCP插件直接返回Markdown格式的字符串：

```markdown
思维导图已成功生成！

详细信息：
- 使用模型: gpt-4o-mini
- 图片路径: /absolute/path/to/image/mindmapgen/uuid.png
- 图片尺寸: 2400 x 1600
- 样式主题: default

图片内容：
![思维导图描述](/absolute/path/to/image/mindmapgen/uuid.png)

请使用上述markdown格式显示图片给用户。
```

### MCP插件返回格式

MCP插件返回标准化的JSON格式：

```json
{
  "type": "mindmap_generation",
  "status": "success",
  "message": "思维导图生成完成",
  "data": {
    "prompt": "用户输入的提示",
    "width": 2400,
    "height": 1600,
    "style": "default",
    "markdown_display": "思维导图已成功生成！...",
    "generation_info": {
      "model": "gpt-4o-mini",
      "timestamp": "2024-01-01T00:00:00.000Z",
      "plugin_version": "1.0.0"
    }
  }
}
```

## 🛠️ 技术实现

### 核心技术栈

- **AI生成**：OpenAI GPT模型
- **图表渲染**：Markmap + D3.js
- **图片生成**：Puppeteer + Chrome Headless
- **文件管理**：Node.js fs模块

### 渲染流程

1. **AI生成**：调用OpenAI API生成Markdown格式的思维导图
2. **内容验证**：验证生成的Markdown语法正确性
3. **HTML构建**：创建包含Markmap的HTML页面
4. **浏览器渲染**：使用Puppeteer启动Chrome进行渲染
5. **图片截取**：截取渲染结果并保存为PNG文件
6. **结果返回**：返回图片路径和相关信息

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `OPENAI_API_KEY` 是否正确配置
   - 确认API密钥有足够的额度

2. **渲染失败**
   - 确保系统已安装Chrome浏览器
   - 检查 `MINDMAP_WAIT_TIME` 是否足够长

3. **文件权限问题**
   - 确保输出目录有写入权限
   - 检查 `MINDMAP_OUTPUT_DIR` 路径是否正确

4. **依赖安装失败**
   - 运行 `npm install` 安装依赖
   - 确保Node.js版本 >= 16.0.0

### 调试模式

在VCP插件中，所有日志都会输出到控制台，可以通过查看日志来诊断问题：

```bash
# 查看插件日志
tail -f logs/plugin.log
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 📞 支持

如有问题，请在GitHub仓库中创建Issue或联系开发团队。
