body {
    font-family: sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f0f0f0;
}
#game-info {
    margin-bottom: 20px;
    text-align: center;
}
#board {
    width: 600px;
    height: 600px;
    border: 2px solid #333;
    position: relative;
    background-color: #fff;
}
.base {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 1px solid #ccc;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    padding: 10px;
    box-sizing: border-box;
}
#base-红 { top: 0; left: 0; background-color: #ffdddd; }
#base-绿 { top: 0; right: 0; background-color: #ddffdd; }
#base-黄 { bottom: 0; right: 0; background-color: #ffffdd; }
#base-蓝 { bottom: 0; left: 0; background-color: #ddddff; }

.piece {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #000;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
}
.piece.红 { background-color: red; color: white; }
.piece.绿 { background-color: green; color: white; }
.piece.黄 { background-color: gold; color: black; }
.piece.蓝 { background-color: blue; color: white; }

.piece.movable {
    box-shadow: 0 0 10px 3px yellow;
    cursor: pointer;
}
.hidden { display: none; }
#winner-message {
    margin-top: 20px;
    font-size: 2em;
    color: green;
}