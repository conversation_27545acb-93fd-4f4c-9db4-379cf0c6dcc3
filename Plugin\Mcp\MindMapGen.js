// Plugin/Mcp/MindMapGen.js - 思维导图生成MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class MindMapGenMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'MindMapGen';
        this.description = '基于AI生成Markmap格式的思维导图并渲染为图片。支持多种样式主题，可自定义图片尺寸。适用于知识梳理、项目规划、学习笔记等场景。';
        this.vcpName = 'MindMapGen';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                prompt: {
                    type: 'string',
                    description: '思维导图内容描述，详细描述要生成的思维导图主题和结构',
                    minLength: 1,
                    maxLength: 4000
                },
                width: {
                    type: 'number',
                    description: '输出图片宽度（像素）',
                    minimum: 800,
                    maximum: 4000,
                    default: 2400
                },
                height: {
                    type: 'number',
                    description: '输出图片高度（像素）',
                    minimum: 600,
                    maximum: 3000,
                    default: 1600
                },
                waitTime: {
                    type: 'number',
                    description: '渲染等待时间（毫秒），用于确保图片完全渲染',
                    minimum: 3000,
                    maximum: 30000,
                    default: 8000
                },
                style: {
                    type: 'string',
                    description: '思维导图样式主题',
                    enum: ['default', 'colorful', 'dark', 'minimal'],
                    default: 'default'
                }
            },
            required: ['prompt']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);

        // 设置默认值
        if (!args.width) args.width = 2400;
        if (!args.height) args.height = 1600;
        if (!args.waitTime) args.waitTime = 8000;
        if (!args.style) args.style = 'default';

        this.log('info', `开始生成思维导图`, {
            prompt: args.prompt.substring(0, 100) + (args.prompt.length > 100 ? '...' : ''),
            width: args.width,
            height: args.height,
            style: args.style
        });

        try {
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);

            // VCP插件现在直接返回Markdown格式的字符串
            if (typeof result === 'string') {
                // 检查是否是错误消息
                if (result.includes('失败') || result.includes('错误')) {
                    return {
                        type: 'mindmap_generation',
                        status: 'error',
                        message: result,
                        data: {
                            prompt: args.prompt,
                            error_details: result,
                            timestamp: new Date().toISOString()
                        }
                    };
                }

                // 成功情况，直接返回Markdown内容
                this.log('success', `思维导图生成成功`);

                return {
                    type: 'mindmap_generation',
                    status: 'success',
                    message: '思维导图生成完成',
                    data: {
                        prompt: args.prompt,
                        width: args.width,
                        height: args.height,
                        style: args.style,
                        markdown_display: result,
                        generation_info: {
                            model: 'gpt-4o-mini',
                            timestamp: new Date().toISOString(),
                            plugin_version: '1.0.0'
                        }
                    }
                };
            }

            // 如果返回的不是字符串，按原来的方式处理
            let parsedResult;
            try {
                parsedResult = typeof result === 'object' ? result : JSON.parse(result);
            } catch (e) {
                parsedResult = { message: result };
            }

            const response = {
                type: 'mindmap_generation',
                status: 'success',
                message: '思维导图生成完成',
                data: {
                    prompt: args.prompt,
                    width: args.width,
                    height: args.height,
                    style: args.style,
                    markdown_display: parsedResult.message || result,
                    generation_info: {
                        model: 'gpt-4o-mini',
                        timestamp: new Date().toISOString(),
                        plugin_version: '1.0.0'
                    }
                }
            };

            this.log('success', `思维导图生成成功`);
            return response;

        } catch (error) {
            this.log('error', `思维导图生成失败: ${error.message}`);
            
            return {
                type: 'mindmap_generation',
                status: 'error',
                message: `思维导图生成失败: ${error.message}`,
                data: {
                    prompt: args.prompt,
                    error_type: error.name || 'UnknownError',
                    error_details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }



    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量
            const requiredEnvVars = ['OPENAI_API_KEY'];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    this.log('warning', `未配置${envVar}环境变量，可能影响AI生成功能`);
                }
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = MindMapGenMcp;
