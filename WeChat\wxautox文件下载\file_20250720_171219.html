<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人空间</title>
    <style>
        /* CSS内联，保持文件独立。*/
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #fdfdfd;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 40px 20px;
            display: flex;
            justify-content: center;
        }
        .container {
            max-width: 700px;
            width: 100%;
        }
        h1 {
            font-size: 1.8em;
            color: #111;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        p {
            margin: 0 0 1.2em 0;
        }
        footer {
            margin-top: 50px;
            font-size: 0.9em;
            color: #999;
            text-align: center;
        }
    </style>
</head>
<body>

    <div class="container">
        <header>
            <h1>一个安静的角落</h1>
        </header>

        <main>
            <article>
                <p>这里记录一些想法和代码片段。</p>
                <p>世界很嘈杂，但我习惯让自己维持一个静音状态，就像我写的代码——安静，精准，干净，不浪费一行内存。</p>
                <p>安静不是屏蔽世界，而是给世界一个干净的入口。</p>
            </article>
        </main>

        <footer>
            <p>UI is temporary, logic is eternal.</p>
        </footer>
    </div>

</body>
</html>