/**
 * MindMapGen 插件测试文件
 * 用于测试思维导图生成功能
 */

const MindMapGenVCP = require('./MindMapGen');
const path = require('path');
const fs = require('fs');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, 'config.env') });

async function testMindMapGeneration() {
    console.log('🧪 开始测试 MindMapGen 插件...\n');

    const plugin = new MindMapGenVCP();

    // 测试用例
    const testCases = [
        {
            name: '基础测试',
            args: {
                prompt: '人工智能的发展历程，包括机器学习、深度学习、自然语言处理等主要分支'
            }
        },
        {
            name: '自定义样式测试',
            args: {
                prompt: '软件开发生命周期管理',
                width: 1920,
                height: 1080,
                style: 'colorful'
            }
        },
        {
            name: '复杂主题测试',
            args: {
                prompt: '构建现代Web应用的技术栈：前端框架、后端服务、数据库设计、部署运维、安全防护',
                style: 'dark'
            }
        }
    ];

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`📋 测试 ${i + 1}: ${testCase.name}`);
        console.log(`📝 提示词: ${testCase.args.prompt.substring(0, 50)}...`);

        try {
            const startTime = Date.now();
            const result = await plugin.execute(JSON.stringify(testCase.args));
            const endTime = Date.now();

            const parsedResult = JSON.parse(result);
            
            if (parsedResult.status === 'success') {
                console.log(`✅ 测试成功 (耗时: ${endTime - startTime}ms)`);
                console.log(`📁 输出文件: ${parsedResult.data.image_path}`);
                console.log(`📏 文件大小: ${formatFileSize(parsedResult.data.generation_info.file_size)}`);
                console.log(`🎨 样式: ${parsedResult.data.style}`);
                
                // 验证文件是否存在
                if (fs.existsSync(parsedResult.data.image_path)) {
                    console.log(`✅ 文件验证通过`);
                } else {
                    console.log(`❌ 文件不存在: ${parsedResult.data.image_path}`);
                }
            } else {
                console.log(`❌ 测试失败: ${parsedResult.message}`);
                console.log(`🔍 错误详情:`, parsedResult.data);
            }

        } catch (error) {
            console.log(`❌ 测试异常: ${error.message}`);
        }

        console.log(''); // 空行分隔
    }

    console.log('🎉 测试完成！');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 检查环境配置
function checkEnvironment() {
    console.log('🔧 检查环境配置...\n');

    const requiredEnvVars = ['OPENAI_API_KEY'];
    const optionalEnvVars = ['OPENAI_API_URL', 'MINDMAP_MODEL', 'MINDMAP_OUTPUT_DIR'];

    let hasErrors = false;

    // 检查必需的环境变量
    for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
            console.log(`❌ 缺少必需的环境变量: ${envVar}`);
            hasErrors = true;
        } else {
            console.log(`✅ ${envVar}: 已配置`);
        }
    }

    // 检查可选的环境变量
    for (const envVar of optionalEnvVars) {
        if (process.env[envVar]) {
            console.log(`✅ ${envVar}: ${process.env[envVar]}`);
        } else {
            console.log(`ℹ️  ${envVar}: 使用默认值`);
        }
    }

    if (hasErrors) {
        console.log('\n❌ 环境配置不完整，请检查 config.env 文件');
        console.log('💡 提示: 复制 config.env.example 为 config.env 并填入正确的配置');
        process.exit(1);
    }

    console.log('\n✅ 环境配置检查通过\n');
}

// 主函数
async function main() {
    try {
        checkEnvironment();
        await testMindMapGeneration();
    } catch (error) {
        console.error('💥 测试过程中发生错误:', error);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    main();
}

module.exports = {
    testMindMapGeneration,
    checkEnvironment
};
