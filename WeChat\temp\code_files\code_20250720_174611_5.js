document.addEventListener('DOMContentLoaded', () => {
    const socket = io();

    const board = document.getElementById('board');
    const currentPlayerSpan = document.getElementById('current-player');
    const diceRollSpan = document.getElementById('dice-roll');
    const rollButton = document.getElementById('roll-button');
    const resetButton = document.getElementById('reset-button');
    const winnerMessage = document.getElementById('winner-message');

    const PLAYERS = ['红', '绿', '黄', '蓝'];
    const START_POSITIONS = { '红': 0, '绿': 13, '黄': 26, '蓝': 39 };
    const PATH = [
        // 定义棋盘格子的屏幕坐标，这里简化处理
        // 一个完整的实现需要精确计算52个格子的(x, y)坐标
    ];

    socket.on('update_state', (state) => {
        console.log('State updated:', state);
        render(state);
    });

    rollButton.addEventListener('click', () => {
        socket.emit('roll_dice');
    });
    
    resetButton.addEventListener('click', () => {
        socket.emit('reset_game');
    });

    function render(state) {
        board.innerHTML = ''; // 清空棋盘
        currentPlayerSpan.textContent = state.currentPlayer;
        diceRollSpan.textContent = state.lastRoll > 0 ? state.lastRoll : '--';
        
        rollButton.disabled = (state.lastRoll > 0) || state.gameOver;

        if (state.gameOver) {
            winnerMessage.textContent = `游戏结束！胜利者是 ${state.winner}！`;
            winnerMessage.classList.remove('hidden');
        } else {
            winnerMessage.classList.add('hidden');
        }

        // 渲染基地
        PLAYERS.forEach(player => {
            const base = document.createElement('div');
            base.className = `base`;
            base.id = `base-${player}`;
            board.appendChild(base);
        });

        // 渲染棋子
        const validMoves = getValidMoves(state);
        for (const player of PLAYERS) {
            state.positions[player].forEach((pos, index) => {
                const piece = document.createElement('div');
                piece.className = `piece ${player}`;
                piece.dataset.player = player;
                piece.dataset.index = index;
                piece.textContent = index + 1;

                if (pos === -1) { // 在基地
                    document.getElementById(`base-${player}`).appendChild(piece);
                } else if (pos === 52) { // 在终点
                    const base = document.getElementById(`base-${player}`);
                    piece.style.opacity = '0.5';
                    base.appendChild(piece);
                } else { // 在路上
                    // 简化定位逻辑，实际项目需要一个坐标映射表
                    const boardPos = (START_POSITIONS[player] + pos) % 52;
                    const { x, y } = calculatePosition(boardPos);
                    piece.style.left = `${x}px`;
                    piece.style.top = `${y}px`;
                    board.appendChild(piece);
                }

                if (validMoves.some(m => m.player === player && m.pieceIndex === index)) {
                    piece.classList.add('movable');
                    piece.addEventListener('click', () => {
                        socket.emit('move_piece', { pieceIndex: index });
                    });
                }
            });
        }
    }

    function getValidMoves(state) {
        const moves = [];
        if (state.lastRoll === 0) return moves;

        const player = state.currentPlayer;
        const roll = state.lastRoll;
        const pieces = state.positions[player];

        for (let i = 0; i < pieces.length; i++) {
            const pos = pieces[i];
            if ((roll === 6 && pos === -1) || (pos !== -1 && pos !== 52)) {
                moves.push({ player, pieceIndex: i });
            }
        }
        return moves;
    }
    
    function calculatePosition(boardPos) {
        // 这是一个简化的位置计算函数，仅用于演示
        // 0-12: bottom, 13-25: left, 26-38: top, 39-51: right
        const size = 600; const margin = 50; const step = (size - margin*2) / 12;
        if (boardPos >= 0 && boardPos <= 12) return { x: margin + boardPos * step, y: size - margin };
        if (boardPos >= 13 && boardPos <= 25) return { x: margin, y: size - margin - (boardPos-12) * step };
        if (boardPos >= 26 && boardPos <= 38) return { x: margin + (boardPos-25) * step, y: margin };
        if (boardPos >= 39 && boardPos <= 51) return { x: size - margin, y: margin + (boardPos-38) * step };
        return {x: size/2, y: size/2}; // 默认位置
    }
});