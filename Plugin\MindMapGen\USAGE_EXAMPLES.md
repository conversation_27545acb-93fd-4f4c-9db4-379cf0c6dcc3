# MindMapGen 使用示例

## 🎯 插件功能验证

✅ **VCP插件**: MindMapGen已成功创建，支持AI生成思维导图  
✅ **MCP插件**: 已创建对应的MCP插件，继承BaseMcpPlugin  
✅ **参数验证**: 支持必需参数`prompt`和可选参数`width`、`height`、`style`、`waitTime`  
✅ **AI生成**: 集成OpenAI API进行智能内容生成  
✅ **图片渲染**: 使用Puppeteer + Markmap渲染高质量PNG图片  
✅ **多样式支持**: 提供4种不同的视觉主题  
✅ **错误处理**: 完善的错误处理和异常捕获机制  

## 📝 VCP调用示例

### 1. 基本用法 - 学习笔记

```
{{MindMapGen}}
参数：{
  "prompt": "机器学习的基本概念：监督学习、无监督学习、强化学习的区别和应用场景"
}
```

**预期输出**: 生成一个包含机器学习分类和应用的思维导图

### 2. 项目规划 - 自定义尺寸

```
{{MindMapGen}}
参数：{
  "prompt": "开发一个电商网站的完整流程：需求分析、技术选型、架构设计、开发实施、测试部署",
  "width": 3200,
  "height": 2400,
  "style": "colorful"
}
```

**预期输出**: 生成一个彩色主题的大尺寸项目规划思维导图

### 3. 知识体系 - 深色主题

```
{{MindMapGen}}
参数：{
  "prompt": "现代前端开发技术栈：HTML5、CSS3、JavaScript ES6+、React/Vue、构建工具、测试框架",
  "style": "dark",
  "waitTime": 10000
}
```

**预期输出**: 生成一个深色主题的前端技术栈思维导图

## 📊 MCP调用示例

### 1. 基本调用

```json
{
  "method": "tools/call",
  "params": {
    "name": "MindMapGen",
    "arguments": {
      "prompt": "人工智能在各行业的应用：医疗、金融、教育、交通、制造业"
    }
  }
}
```

### 2. 完整参数调用

```json
{
  "method": "tools/call",
  "params": {
    "name": "MindMapGen",
    "arguments": {
      "prompt": "软件架构设计模式：MVC、MVP、MVVM、微服务、事件驱动架构",
      "width": 2800,
      "height": 2000,
      "style": "minimal",
      "waitTime": 12000
    }
  }
}
```

### 3. 教学场景

```json
{
  "method": "tools/call",
  "params": {
    "name": "MindMapGen",
    "arguments": {
      "prompt": "数据结构与算法学习路径：数组、链表、栈、队列、树、图、排序算法、搜索算法",
      "style": "colorful"
    }
  }
}
```

## 🎨 样式主题展示

### Default 主题
- **特点**: 经典白色背景，黑色文字
- **适用**: 正式文档、学术报告
- **示例**: 
```json
{"prompt": "企业管理理论", "style": "default"}
```

### Colorful 主题
- **特点**: 彩色渐变背景，丰富视觉效果
- **适用**: 创意展示、培训材料
- **示例**: 
```json
{"prompt": "创新思维方法", "style": "colorful"}
```

### Dark 主题
- **特点**: 深色背景，白色文字
- **适用**: 技术文档、暗色环境
- **示例**: 
```json
{"prompt": "网络安全防护体系", "style": "dark"}
```

### Minimal 主题
- **特点**: 简约风格，突出内容
- **适用**: 简洁展示、概念图
- **示例**: 
```json
{"prompt": "设计思维流程", "style": "minimal"}
```

## 📋 实际应用场景

### 1. 会议记录整理

**输入提示**:
```
"今天的产品规划会议要点：用户需求分析、功能优先级排序、技术实现方案、时间节点安排、资源分配"
```

**应用价值**: 将会议内容可视化，便于后续跟进和分享

### 2. 学习笔记制作

**输入提示**:
```
"深度学习基础知识：神经网络结构、反向传播算法、激活函数、损失函数、优化器、正则化技术"
```

**应用价值**: 帮助学生理解复杂概念的层次关系

### 3. 项目架构设计

**输入提示**:
```
"微服务架构设计：服务拆分原则、API网关、服务发现、配置中心、监控告警、容器化部署"
```

**应用价值**: 清晰展示系统架构的各个组件和关系

### 4. 业务流程梳理

**输入提示**:
```
"客户服务流程优化：问题接收、分类处理、专家分配、解决方案、客户反馈、质量评估"
```

**应用价值**: 识别流程瓶颈，优化业务效率

## 🔧 高级用法

### 1. 批量生成

可以通过脚本批量生成多个思维导图：

```javascript
const topics = [
  "人工智能发展历程",
  "区块链技术应用",
  "云计算服务模式"
];

for (const topic of topics) {
  await mindMapGen.execute(JSON.stringify({
    prompt: topic,
    style: "default"
  }));
}
```

### 2. 自定义输出目录

```json
{
  "prompt": "项目管理知识体系",
  "width": 2400,
  "height": 1600
}
```

配置环境变量：
```env
MINDMAP_OUTPUT_DIR=./custom/output/path
```

### 3. 性能优化

对于复杂主题，可以调整等待时间：

```json
{
  "prompt": "复杂的企业级系统架构设计",
  "waitTime": 15000
}
```

## 📈 输出质量优化建议

### 1. 提示词优化

**好的提示词**:
```
"现代Web开发技术栈：前端框架React/Vue、后端Node.js/Python、数据库MySQL/MongoDB、部署Docker/K8s"
```

**避免的提示词**:
```
"做一个关于编程的图"
```

### 2. 结构层次

建议提示词包含：
- 主题明确
- 分支清晰
- 层次合理（3-5级）
- 内容具体

### 3. 尺寸选择

- **演示用**: 1920x1080
- **打印用**: 3200x2400
- **网页用**: 1600x1200
- **移动端**: 800x600

## 🚀 集成示例

### 与其他插件配合

1. **FileProcessor + MindMapGen**: 分析文档后生成思维导图
2. **MultiAIConsensus + MindMapGen**: 多AI协商后生成综合思维导图
3. **TavilySearch + MindMapGen**: 搜索资料后生成知识图谱

### API集成

```javascript
// 在其他插件中调用
const mindMapResult = await pluginManager.executePlugin('MindMapGen', JSON.stringify({
  prompt: "基于分析结果生成的思维导图",
  style: "colorful"
}));
```

## 📊 性能指标

### 典型生成时间

- **简单主题** (2-3级): 15-30秒
- **中等复杂** (3-4级): 30-60秒  
- **复杂主题** (4-5级): 60-120秒

### 文件大小

- **标准尺寸** (2400x1600): 500KB-2MB
- **高清尺寸** (3200x2400): 1MB-4MB
- **压缩优化**: 可减少30-50%

## 🔍 故障排除

### 常见问题解决

1. **生成时间过长**: 增加 `waitTime` 参数
2. **图片模糊**: 增加 `width` 和 `height`
3. **样式不理想**: 尝试不同的 `style` 选项
4. **内容不准确**: 优化 `prompt` 描述

### 调试技巧

1. 查看控制台输出
2. 检查生成的Markdown内容
3. 验证环境变量配置
4. 测试API连接状态
