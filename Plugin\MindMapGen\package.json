{"name": "mindmapgen-vcp-plugin", "version": "1.0.0", "description": "基于AI生成Markmap格式的思维导图并渲染为图片的VCP插件", "main": "MindMapGen.js", "scripts": {"test": "node test.js", "install-deps": "npm install", "check-deps": "npm list"}, "dependencies": {"axios": "^1.6.0", "puppeteer": "^21.0.0", "dotenv": "^16.0.0"}, "devDependencies": {}, "engines": {"node": ">=16.0.0"}, "keywords": ["mindmap", "markmap", "ai-generation", "visualization", "vcp-plugin"], "author": "VCPToolBox", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/vcptoolbox"}, "bugs": {"url": "https://github.com/your-repo/vcptoolbox/issues"}, "homepage": "https://github.com/your-repo/vcptoolbox#readme"}