/**
 * 思维导图生成插件 - VCP版本
 * 基于Markmap生成思维导图，支持AI生成内容和图片渲染
 * 参考Yunzai框架的AiMindMapTool实现
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const axios = require('axios');

class MindMapGenVCP {
    constructor() {
        this.name = 'MindMapGen';
        this.description = '基于AI生成Markmap格式的思维导图并渲染为图片';
        
        // 插件配置
        this.config = {
            apiUrl: process.env.OPENAI_API_URL || 'https://api.openai.com',
            apiKey: process.env.OPENAI_API_KEY,
            model: process.env.MINDMAP_MODEL || 'gpt-4o-mini',
            timeout: parseInt(process.env.MINDMAP_TIMEOUT) || 30000,
            projectBasePath: process.env.PROJECT_BASE_PATH || process.cwd(),
            defaultWidth: parseInt(process.env.MINDMAP_WIDTH) || 2400,
            defaultHeight: parseInt(process.env.MINDMAP_HEIGHT) || 1600,
            waitTime: parseInt(process.env.MINDMAP_WAIT_TIME) || 8000
        };

        // 参数定义
        this.parameters = {
            type: "object",
            properties: {
                prompt: {
                    type: 'string',
                    description: '思维导图内容描述',
                    minLength: 1,
                    maxLength: 4000
                },
                width: {
                    type: 'number',
                    description: '输出图片宽度',
                    default: this.config.defaultWidth,
                    minimum: 800,
                    maximum: 4000
                },
                height: {
                    type: 'number',
                    description: '输出图片高度',
                    default: this.config.defaultHeight,
                    minimum: 600,
                    maximum: 3000
                },
                waitTime: {
                    type: 'number',
                    description: '渲染等待时间（毫秒）',
                    default: this.config.waitTime,
                    minimum: 3000,
                    maximum: 30000
                },
                style: {
                    type: 'string',
                    description: '思维导图样式主题',
                    enum: ['default', 'colorful', 'dark', 'minimal'],
                    default: 'default'
                }
            },
            required: ['prompt'],
            additionalProperties: false
        };

        // 确保输出目录存在
        this.ensureOutputDir();
    }

    /**
     * 确保输出目录存在
     */
    async ensureOutputDir() {
        try {
            const mindMapImageDir = path.join(this.config.projectBasePath, 'image', 'mindmapgen');
            await fs.mkdir(mindMapImageDir, { recursive: true });
        } catch (error) {
            console.error('创建输出目录失败:', error);
        }
    }

    /**
     * 生成UUID
     * @returns {string} - UUID字符串
     */
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * 生成系统提示词
     * @param {Object} opts - 用户选项
     * @returns {string} - 系统提示词
     */
    generateSystemPrompt(opts) {
        return `你是一个专业的思维导图生成助手。请根据用户的描述生成符合Markdown语法的思维导图代码。

要求：
1. 只输出Markdown代码，不要其他解释或代码块标记
2. 确保语法正确，适合Markmap渲染
3. 使用#表示主节点，##表示一级子节点，###表示二级子节点，以此类推
4. 合理组织层级结构，最多5级
5. 使用简洁清晰的描述
6. 避免非Markdown内容（如HTML标签或其他格式）
7. 确保每个节点有清晰的标题
8. 支持中文内容，确保编码正确
9. 节点内容要有逻辑性和层次性
10. 可以使用适当的emoji来增强视觉效果

示例格式：
# 主题
## 分支1
### 子分支1.1
### 子分支1.2
## 分支2
### 子分支2.1
#### 详细内容2.1.1
#### 详细内容2.1.2`;
    }

    /**
     * 验证Markdown内容是否有效
     * @param {string} markdown - Markdown内容
     * @returns {boolean} - 是否有效
     */
    validateMarkdown(markdown) {
        if (!markdown || typeof markdown !== 'string') {
            return false;
        }

        const lines = markdown.split('\n').map(line => line.trim()).filter(line => line.length > 0);
        
        // 检查是否有标题行
        const hasHeaders = lines.some(line => line.startsWith('#'));
        
        // 检查是否包含代码块标记（不应该有）
        const hasCodeBlocks = lines.some(line => line.includes('```'));
        
        // 检查是否有主标题
        const hasMainHeader = lines.some(line => line.startsWith('# '));
        
        return hasHeaders && !hasCodeBlocks && hasMainHeader;
    }

    /**
     * 调用AI生成思维导图内容
     * @param {string} prompt - 用户提示
     * @returns {Promise<string>} - 生成的Markdown内容
     */
    async generateMindMapContent(prompt) {
        const messages = [
            {
                role: 'system',
                content: this.generateSystemPrompt()
            },
            {
                role: 'user',
                content: `请根据以下描述生成思维导图：${prompt}`
            }
        ];

        try {
            const response = await axios.post(
                `${this.config.apiUrl}/v1/chat/completions`,
                {
                    model: this.config.model,
                    messages: messages,
                    temperature: 0.7,
                    max_tokens: 2000
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.config.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: this.config.timeout
                }
            );

            if (response.data?.choices?.[0]?.message?.content) {
                return response.data.choices[0].message.content.trim();
            }

            throw new Error('AI响应格式错误');

        } catch (error) {
            console.error('AI生成失败:', error.message);
            throw new Error(`AI生成失败: ${error.message}`);
        }
    }

    /**
     * 生成样式CSS
     * @param {string} style - 样式主题
     * @returns {string} - CSS样式
     */
    generateStyleCSS(style) {
        const styles = {
            default: `
                body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; background: #fff; }
                #markmap { width: 100%; height: 100%; }
                .markmap-node { font-family: 'Microsoft YaHei', Arial, sans-serif; }
            `,
            colorful: `
                body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                #markmap { width: 100%; height: 100%; }
                .markmap-node { font-family: 'Microsoft YaHei', Arial, sans-serif; font-weight: bold; }
            `,
            dark: `
                body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; background: #1a1a1a; }
                #markmap { width: 100%; height: 100%; }
                .markmap-node { font-family: 'Microsoft YaHei', Arial, sans-serif; color: #fff; }
            `,
            minimal: `
                body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; background: #f8f9fa; }
                #markmap { width: 100%; height: 100%; }
                .markmap-node { font-family: 'Microsoft YaHei', Arial, sans-serif; font-weight: 300; }
            `
        };

        return styles[style] || styles.default;
    }

    /**
     * 使用Node.js渲染思维导图
     * @param {string} markdownContent - Markdown内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} - 输出文件路径
     */
    async renderMindMapWithNode(markdownContent, options = {}) {
        const { width = this.config.defaultWidth, height = this.config.defaultHeight, style = 'default' } = options;

        // 生成唯一文件名
        const generatedFileName = `${this.generateUUID()}.png`;
        const mindMapImageDir = path.join(this.config.projectBasePath, 'image', 'mindmapgen');
        const outputPath = path.join(mindMapImageDir, generatedFileName);

        // 创建临时HTML文件
        const tempHtmlPath = path.join(mindMapImageDir, `temp-${Date.now()}.html`);

        const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.jsdelivr.net/npm/d3@6"></script>
    <script src="https://cdn.jsdelivr.net/npm/markmap-lib@0.18.10"></script>
    <script src="https://cdn.jsdelivr.net/npm/markmap-view@0.18.10"></script>
    <style>
        ${this.generateStyleCSS(style)}
    </style>
</head>
<body>
    <svg id="markmap" width="${width}" height="${height}"></svg>
    <script>
        const { Transformer } = markmap;
        const { Markmap } = markmap;

        const transformer = new Transformer();
        const { root } = transformer.transform(\`${markdownContent.replace(/`/g, '\\`')}\`);

        const svg = document.getElementById('markmap');
        const mm = Markmap.create(svg, null, root);

        setTimeout(() => {
            mm.fit();
            console.log('Markmap渲染完成');
        }, 100);
    </script>
</body>
</html>`;

        try {
            // 写入临时HTML文件
            await fs.writeFile(tempHtmlPath, htmlContent, 'utf8');

            // 使用puppeteer渲染
            await this.renderWithPuppeteer(tempHtmlPath, outputPath, { width, height });

            // 清理临时文件
            await fs.unlink(tempHtmlPath).catch(() => {});

            return outputPath;

        } catch (error) {
            // 清理临时文件
            await fs.unlink(tempHtmlPath).catch(() => {});
            throw error;
        }
    }

    /**
     * 使用Puppeteer渲染HTML为图片
     * @param {string} htmlPath - HTML文件路径
     * @param {string} outputPath - 输出图片路径
     * @param {Object} options - 渲染选项
     */
    async renderWithPuppeteer(htmlPath, outputPath, options = {}) {
        const { width, height } = options;

        return new Promise((resolve, reject) => {
            // 使用子进程调用puppeteer脚本
            const puppeteerScript = `
const puppeteer = require('puppeteer');
const path = require('path');

(async () => {
    try {
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
        });

        const page = await browser.newPage();
        await page.setViewport({ width: ${width}, height: ${height} });

        // 加载HTML文件
        await page.goto('file://' + path.resolve('${htmlPath}'), {
            waitUntil: 'networkidle0',
            timeout: 30000
        });

        // 等待渲染完成
        await page.waitForFunction('document.querySelector("#markmap").children.length > 0', { timeout: 15000 });
        await page.waitForTimeout(${this.config.waitTime});

        // 截图
        await page.screenshot({
            path: '${outputPath}',
            fullPage: false,
            type: 'png',
            clip: { x: 0, y: 0, width: ${width}, height: ${height} }
        });

        await browser.close();
        console.log('渲染完成');

    } catch (error) {
        console.error('渲染失败:', error);
        process.exit(1);
    }
})();
`;

            // 创建临时脚本文件
            const tempScriptPath = path.join(mindMapImageDir, `render-${Date.now()}.js`);

            fs.writeFile(tempScriptPath, puppeteerScript, 'utf8').then(() => {
                const child = spawn('node', [tempScriptPath], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    cwd: process.cwd()
                });

                let output = '';
                let errorOutput = '';

                child.stdout.on('data', (data) => {
                    output += data.toString();
                });

                child.stderr.on('data', (data) => {
                    errorOutput += data.toString();
                });

                child.on('close', async (code) => {
                    // 清理临时脚本
                    await fs.unlink(tempScriptPath).catch(() => {});

                    if (code === 0) {
                        resolve();
                    } else {
                        reject(new Error(`Puppeteer渲染失败: ${errorOutput || output}`));
                    }
                });

                child.on('error', async (error) => {
                    // 清理临时脚本
                    await fs.unlink(tempScriptPath).catch(() => {});
                    reject(new Error(`启动Puppeteer失败: ${error.message}`));
                });
            }).catch(reject);
        });
    }

    /**
     * 主执行方法 - VCP插件入口
     * @param {string} argsJson - JSON格式的参数字符串
     * @returns {Promise<string>} - JSON格式的执行结果
     */
    async execute(argsJson) {
        try {
            // 解析参数
            let args;
            try {
                args = typeof argsJson === 'string' ? JSON.parse(argsJson) : argsJson;
            } catch (error) {
                throw new Error(`参数解析失败: ${error.message}`);
            }

            // 验证必需参数
            if (!args.prompt) {
                throw new Error('缺少必需参数: prompt');
            }

            // 设置默认值
            const options = {
                prompt: args.prompt,
                width: args.width || this.config.defaultWidth,
                height: args.height || this.config.defaultHeight,
                waitTime: args.waitTime || this.config.waitTime,
                style: args.style || 'default'
            };

            console.log('开始生成思维导图:', options);

            // 第一步：AI生成思维导图内容
            console.log('正在调用AI生成思维导图内容...');
            const markdownContent = await this.generateMindMapContent(options.prompt);

            if (!this.validateMarkdown(markdownContent)) {
                throw new Error('生成的Markdown内容无效，请重试');
            }

            console.log('AI生成完成，Markdown内容长度:', markdownContent.length);

            // 第二步：渲染为图片
            console.log('正在渲染思维导图图片...');
            const result = await this.renderMindMapWithNode(markdownContent, {
                width: options.width,
                height: options.height,
                style: options.style,
                prompt: options.prompt
            });

            console.log('思维导图生成完成');
            return result;

        } catch (error) {
            console.error('思维导图生成失败:', error);
            return `思维导图生成失败: ${error.message}`;
        }
    }

    /**
     * 获取文件大小
     * @param {string} filePath - 文件路径
     * @returns {Promise<number>} - 文件大小（字节）
     */
    async getFileSize(filePath) {
        try {
            const stats = await fs.stat(filePath);
            return stats.size;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 清理旧文件（可选功能）
     * @param {number} maxAge - 最大文件年龄（毫秒）
     */
    async cleanupOldFiles(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
        try {
            const mindMapImageDir = path.join(this.config.projectBasePath, 'image', 'mindmapgen');
            const files = await fs.readdir(mindMapImageDir);
            const now = Date.now();

            for (const file of files) {
                if (file.endsWith('.png')) {
                    const filePath = path.join(mindMapImageDir, file);
                    const stats = await fs.stat(filePath);

                    if (now - stats.mtime.getTime() > maxAge) {
                        await fs.unlink(filePath);
                        console.log('清理旧文件:', file);
                    }
                }
            }
        } catch (error) {
            console.error('清理文件失败:', error);
        }
    }
}

module.exports = MindMapGenVCP;
