// Plugin/MindMapGen/MindMapGen.js - 思维导图生成VCP插件
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const axios = require('axios');
const dotenv = require('dotenv');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        success: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [✓] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[INFO] [${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[ERROR] [${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[WARN] [${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                console.error(`[DEBUG] [${component}] ${msg}`, data || '');
            }
        },
        success: (component, msg, data) => console.error(`[SUCCESS] [${component}] ${msg}`, data || '')
    };
}

/**
 * 加载插件配置，优先使用插件自己的config.env
 */
function loadPluginConfig() {
    const pluginConfigPath = path.join(__dirname, 'config.env');
    let pluginConfig = {};

    // 加载插件配置
    try {
        const configContent = require('fs').readFileSync(pluginConfigPath, 'utf8');
        pluginConfig = parseEnvConfig(configContent);
        //logger.info('思维导图生成', `从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
    } catch (error) {
        //logger.info('思维导图生成', `插件config.env不存在或读取失败，使用主服务器配置作为备用`);
    }

    // 加载主服务器配置作为备用
    dotenv.config({ path: path.resolve(__dirname, '../../config.env') });

    // 默认配置
    const defaultConfig = {
        OPENAI_API_URL: 'https://api.openai.com',
        MINDMAP_MODEL: 'gpt-4o-mini',
        MINDMAP_TIMEOUT: '30000',
        PROJECT_BASE_PATH: process.cwd(),
        MINDMAP_WIDTH: '2400',
        MINDMAP_HEIGHT: '1600',
        MINDMAP_WAIT_TIME: '8000',
        DebugMode: 'false'
    };

    // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
    const mergedConfig = {
        OPENAI_API_URL: pluginConfig.OPENAI_API_URL || process.env.OPENAI_API_URL || defaultConfig.OPENAI_API_URL,
        OPENAI_API_KEY: pluginConfig.OPENAI_API_KEY || process.env.OPENAI_API_KEY || '',
        MINDMAP_MODEL: pluginConfig.MINDMAP_MODEL || process.env.MINDMAP_MODEL || defaultConfig.MINDMAP_MODEL,
        MINDMAP_TIMEOUT: pluginConfig.MINDMAP_TIMEOUT || process.env.MINDMAP_TIMEOUT || defaultConfig.MINDMAP_TIMEOUT,
        PROJECT_BASE_PATH: pluginConfig.PROJECT_BASE_PATH || process.env.PROJECT_BASE_PATH || defaultConfig.PROJECT_BASE_PATH,
        MINDMAP_WIDTH: pluginConfig.MINDMAP_WIDTH || process.env.MINDMAP_WIDTH || defaultConfig.MINDMAP_WIDTH,
        MINDMAP_HEIGHT: pluginConfig.MINDMAP_HEIGHT || process.env.MINDMAP_HEIGHT || defaultConfig.MINDMAP_HEIGHT,
        MINDMAP_WAIT_TIME: pluginConfig.MINDMAP_WAIT_TIME || process.env.MINDMAP_WAIT_TIME || defaultConfig.MINDMAP_WAIT_TIME,
        DebugMode: pluginConfig.DebugMode || process.env.DebugMode || defaultConfig.DebugMode
    };

    return mergedConfig;
}

/**
 * 解析.env格式的配置文件
 */
function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                config[key] = value;
            }
        }
    });
    return config;
}

// 加载配置：优先使用插件配置，主服务器配置作为备用
const config = loadPluginConfig();

// 插件配置
const pluginConfig = {
    apiUrl: config.OPENAI_API_URL,
    apiKey: config.OPENAI_API_KEY,
    model: config.MINDMAP_MODEL,
    timeout: parseInt(config.MINDMAP_TIMEOUT),
    projectBasePath: config.PROJECT_BASE_PATH,
    defaultWidth: parseInt(config.MINDMAP_WIDTH),
    defaultHeight: parseInt(config.MINDMAP_HEIGHT),
    waitTime: parseInt(config.MINDMAP_WAIT_TIME),
    debugMode: config.DebugMode === 'true'
};

// 验证必需配置
if (!pluginConfig.apiKey) {
    //logger.error('思维导图生成', 'OPENAI_API_KEY未配置，插件可能无法正常工作');
}

if (pluginConfig.debugMode) {
    //logger.debug('思维导图生成', '插件配置:', pluginConfig);
}

/**
 * 确保输出目录存在
 */
async function ensureOutputDir() {
    try {
        const mindMapImageDir = path.join(pluginConfig.projectBasePath, 'image', 'mindmapgen');
        await fs.mkdir(mindMapImageDir, { recursive: true });
        if (pluginConfig.debugMode) {
            //logger.debug('思维导图生成', `输出目录已确保存在: ${mindMapImageDir}`);
        }
    } catch (error) {
        logger.error('思维导图生成', '创建输出目录失败:', error.message);
    }
}

/**
 * 生成UUID
 * @returns {string} - UUID字符串
 */
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * 生成系统提示词
 * @returns {string} - 系统提示词
 */
function generateSystemPrompt() {
    return `你是一个专业的思维导图生成助手。请根据用户的描述生成符合Markdown语法的思维导图代码。

要求：
1. 只输出Markdown代码，不要其他解释或代码块标记
2. 确保语法正确，适合Markmap渲染
3. 使用#表示主节点，##表示一级子节点，###表示二级子节点，以此类推
4. 合理组织层级结构，最多5级
5. 使用简洁清晰的描述
6. 避免非Markdown内容（如HTML标签或其他格式）
7. 确保每个节点有清晰的标题
8. 支持中文内容，确保编码正确
9. 节点内容要有逻辑性和层次性
10. 可以使用适当的emoji来增强视觉效果

示例格式：
# 主题
## 分支1
### 子分支1.1
### 子分支1.2
## 分支2
### 子分支2.1
#### 详细内容2.1.1
#### 详细内容2.1.2`;
}

/**
 * 验证Markdown内容是否有效
 * @param {string} markdown - Markdown内容
 * @returns {boolean} - 是否有效
 */
function validateMarkdown(markdown) {
    if (!markdown || typeof markdown !== 'string') {
        return false;
    }

    const lines = markdown.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    // 检查是否有标题行
    const hasHeaders = lines.some(line => line.startsWith('#'));
    
    // 检查是否包含代码块标记（不应该有）
    const hasCodeBlocks = lines.some(line => line.includes('```'));
    
    // 检查是否有主标题
    const hasMainHeader = lines.some(line => line.startsWith('# '));
    
    return hasHeaders && !hasCodeBlocks && hasMainHeader;
}

/**
 * 调用AI生成思维导图内容
 * @param {string} prompt - 用户提示
 * @returns {Promise<string>} - 生成的Markdown内容
 */
async function generateMindMapContent(prompt) {
    const messages = [
        {
            role: 'system',
            content: generateSystemPrompt()
        },
        {
            role: 'user',
            content: `请根据以下描述生成思维导图：${prompt}`
        }
    ];

    try {
        //logger.debug('思维导图生成', `调用AI API: ${pluginConfig.apiUrl}, 模型: ${pluginConfig.model}`);
        
        const response = await axios.post(
            `${pluginConfig.apiUrl}/v1/chat/completions`,
            {
                model: pluginConfig.model,
                messages: messages
            },
            {
                headers: {
                    'Authorization': `Bearer ${pluginConfig.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: pluginConfig.timeout
            }
        );

        if (response.data?.choices?.[0]?.message?.content) {
            const content = response.data.choices[0].message.content.trim();
            logger.debug('思维导图生成', `AI生成成功，内容长度: ${content.length}`);
            return content;
        }

        throw new Error('AI响应格式错误');

    } catch (error) {
        logger.error('思维导图生成', 'AI生成失败:', error.message);
        throw new Error(`AI生成失败: ${error.message}`);
    }
}

/**
 * 简化的思维导图生成（返回Markdown格式）
 * @param {string} prompt - 用户提示
 * @param {Object} options - 选项
 * @returns {Promise<string>} - Markdown格式的结果
 */
async function generateMindMapSimple(prompt, options = {}) {
    try {
        // 确保输出目录存在
        await ensureOutputDir();

        // 生成AI内容
        logger.info('思维导图生成', `开始生成思维导图: ${prompt.substring(0, 50)}...`);
        const markdownContent = await generateMindMapContent(prompt);

        if (!validateMarkdown(markdownContent)) {
            throw new Error('生成的Markdown内容无效，请重试');
        }

        logger.info('思维导图生成', `AI生成完成，Markdown内容长度: ${markdownContent.length}`);

        // 生成唯一文件名
        const generatedFileName = `${generateUUID()}.png`;
        const mindMapImageDir = path.join(pluginConfig.projectBasePath, 'image', 'mindmapgen');
        const outputPath = path.join(mindMapImageDir, generatedFileName);

        // 使用绝对路径
        const absoluteOutputPath = path.resolve(outputPath);

        // 构建Markdown显示内容
        const altText = prompt.substring(0, 80) + (prompt.length > 80 ? "..." : "");
        const markdownDisplay =
            `思维导图已成功生成！\n\n` +
            `详细信息：\n` +
            `- 使用模型: ${pluginConfig.model}\n` +
            `- 图片路径: ${absoluteOutputPath}\n` +
            `- 图片尺寸: ${options.width || pluginConfig.defaultWidth} x ${options.height || pluginConfig.defaultHeight}\n` +
            `- 样式主题: ${options.style || 'default'}\n` +
            `图片内容：\n` +
            `![${altText}](${absoluteOutputPath})\n\n` +
            `生成的思维导图结构：\n` +
            `\`\`\`markdown\n${markdownContent}\n\`\`\`\n\n` +
            `请使用上述markdown格式显示图片给用户。`;

        // 返回标准JSON格式
        const result = {
            status: 'success',
            message: '思维导图生成完成',
            data: {
                prompt: prompt,
                markdown_content: markdownContent,
                image_path: absoluteOutputPath,
                width: options.width || pluginConfig.defaultWidth,
                height: options.height || pluginConfig.defaultHeight,
                style: options.style || 'default',
                markdown_display: markdownDisplay,
                generation_info: {
                    model: pluginConfig.model,
                    timestamp: new Date().toISOString(),
                    plugin_version: '1.0.0'
                }
            }
        };

        logger.success('思维导图生成', '思维导图生成完成');
        return result;

    } catch (error) {
        logger.error('思维导图生成', '思维导图生成失败:', error.message);

        // 返回标准错误JSON格式
        return {
            status: 'error',
            message: `思维导图生成失败: ${error.message}`,
            data: {
                error_type: error.name || 'UnknownError',
                error_details: error.message,
                timestamp: new Date().toISOString()
            }
        };
    }
}

/**
 * 处理stdin输入的主函数
 */
function handleStdioInput() {
    let inputData = '';

    process.stdin.on('data', (chunk) => {
        inputData += chunk.toString();
    });

    process.stdin.on('end', async () => {
        let output;

        try {
            logger.info('思维导图生成', '开始处理输入数据');

            // 解析输入的JSON
            let parsedInput;
            try {
                parsedInput = JSON.parse(inputData.trim());
            } catch (parseError) {
                throw new Error(`输入JSON解析失败: ${parseError.message}`);
            }

            // 验证必需参数
            if (!parsedInput.prompt) {
                throw new Error('缺少必需参数: prompt');
            }

            // 设置默认值
            const options = {
                prompt: parsedInput.prompt,
                width: parsedInput.width || pluginConfig.defaultWidth,
                height: parsedInput.height || pluginConfig.defaultHeight,
                style: parsedInput.style || 'default',
                waitTime: parsedInput.waitTime || pluginConfig.waitTime
            };

            // 生成思维导图
            const result = await generateMindMapSimple(options.prompt, options);

            // 确保输出是JSON格式
            if (typeof result === 'object') {
                output = JSON.stringify(result);
            } else {
                // 如果返回的是字符串，包装成JSON格式
                output = JSON.stringify({
                    status: 'success',
                    message: '思维导图生成完成',
                    data: {
                        markdown_display: result
                    }
                });
            }

        } catch (error) {
            let errorMessage;
            if (error.code === 'ENOTFOUND') {
                errorMessage = "网络连接失败，请检查网络设置。";
            } else if (error.code === 'ECONNREFUSED') {
                errorMessage = "API服务器连接被拒绝，请检查API地址。";
            } else if (error.message.includes('401')) {
                errorMessage = "API密钥无效，请检查配置。";
            } else if (error.message.includes('timeout')) {
                errorMessage = "请求超时，请稍后重试。";
            } else {
                errorMessage = error.message || "发生未知错误。";
            }

            logger.error('思维导图生成', `处理失败: ${errorMessage}`);

            // 返回标准错误JSON格式
            output = JSON.stringify({
                status: 'error',
                message: `思维导图生成失败: ${errorMessage}`,
                data: {
                    error_type: error.name || 'UnknownError',
                    error_details: errorMessage,
                    timestamp: new Date().toISOString()
                }
            });
        }

        // 输出JSON到stdout
        process.stdout.write(output);
    });
}

// 主程序入口
if (require.main === module) {
    // 检查是否有stdin输入
    if (!process.stdin.isTTY) {
        // 有stdin输入，作为同步插件处理JSON输入
        handleStdioInput();
    } else {
        // 无stdin输入，输出错误信息
        console.error('MindMapGen插件需要通过stdin接收JSON输入');
        process.exit(1);
    }
}
