# 思维导图生成器配置文件示例
# 复制此文件为 config.env 并填入实际配置

# OpenAI API 配置
OPENAI_API_URL=https://api.openai.com
OPENAI_API_KEY=your_openai_api_key_here

# 思维导图生成配置
MINDMAP_MODEL=gpt-4o-mini
MINDMAP_TIMEOUT=30000

# 项目路径配置（图片将保存到 PROJECT_BASE_PATH/image/mindmapgen/ 目录）
PROJECT_BASE_PATH=../../

# 输出配置
MINDMAP_WIDTH=2400
MINDMAP_HEIGHT=1600
MINDMAP_WAIT_TIME=8000

# 可选：使用其他兼容的API服务
# OPENAI_API_URL=https://api.deepseek.com
# OPENAI_API_KEY=your_deepseek_api_key

# 可选：使用本地模型服务
# OPENAI_API_URL=http://localhost:11434/v1
# OPENAI_API_KEY=ollama

# 调试模式 (true/false)
DebugMode=false
