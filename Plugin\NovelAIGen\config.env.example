# NovelAIGen 动漫图片生成插件配置示例
# 复制此文件为 config.env 并根据需要修改配置

# ===========================================
# 配置继承说明
# ===========================================
# 1. 如果以下配置项留空，将自动继承主配置文件(config.env)中的对应设置
# 2. 插件配置优先级高于主配置
# 3. 如果两个配置文件都未设置，将使用默认值

# ===========================================
# API配置
# ===========================================
# YuanPlus API密钥 - 留空则继承主配置文件中的 YUANPLUS_API_KEY
YUANPLUS_API_KEY=

# ===========================================
# 角色外貌特征配置
# ===========================================
# 是否启用角色外貌特征自动应用
ENABLE_CHARACTER_APPEARANCE=true

# 默认角色外貌特征（如果全局配置中没有CharAppearance时使用）
DEFAULT_CHARACTER_APPEARANCE=best quality, ultra-detailed, absurdres, 1girl, anime style

# ===========================================
# 特定角色外貌配置
# ===========================================
# 格式：角色名_CHARACTER_APPEARANCE=外貌描述
# 当用户要求画特定角色时，系统会自动查找对应的外貌配置

# 示例角色配置
ALICE_CHARACTER_APPEARANCE=1girl, blonde hair, blue eyes, school uniform, cute smile
BOB_CHARACTER_APPEARANCE=1boy, brown hair, green eyes, casual clothes, friendly expression
MIKU_CHARACTER_APPEARANCE=1girl, long turquoise twin tails, blue eyes, futuristic outfit, vocaloid
SAKURA_CHARACTER_APPEARANCE=1girl, pink hair, green eyes, magical girl outfit, cherry blossoms

# ===========================================
# 使用说明
# ===========================================
# 1. 当用户说"画一张Alice的照片"时，设置include_character=true，character_names=["ALICE"]
# 2. 系统会自动查找ALICE_CHARACTER_APPEARANCE配置并添加到提示词中
# 3. 支持多个角色：character_names=["ALICE", "BOB"]会同时使用两个角色的外貌特征
# 4. 如果找不到特定角色配置，会使用DEFAULT_CHARACTER_APPEARANCE作为备用
# 5. 如果插件配置为空，会自动继承主配置文件中的CharAppearance设置