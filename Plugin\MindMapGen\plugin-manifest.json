{"name": "MindMapGen", "displayName": "思维导图生成器", "version": "1.0.0", "description": "基于AI生成Markmap格式的思维导图并渲染为图片。支持多种样式主题，可自定义图片尺寸，适用于知识梳理、项目规划、学习笔记等场景。", "pluginType": "synchronous", "entryPoint": {"script": "MindMapGen.js"}, "communication": {"protocol": "stdio"}, "configSchema": {"OPENAI_API_URL": {"type": "string", "description": "OpenAI API 基础URL", "required": false, "default": "https://api.openai.com"}, "OPENAI_API_KEY": {"type": "string", "description": "OpenAI API 密钥，用于AI生成思维导图内容", "required": true}, "MINDMAP_MODEL": {"type": "string", "description": "用于生成思维导图的AI模型", "required": false, "default": "gpt-4o-mini"}, "MINDMAP_TIMEOUT": {"type": "number", "description": "AI API调用超时时间（毫秒）", "required": false, "default": 30000}, "PROJECT_BASE_PATH": {"type": "string", "description": "项目基础路径，图片将保存到 PROJECT_BASE_PATH/image/mindmapgen/ 目录", "required": false, "default": "../../"}, "MINDMAP_WIDTH": {"type": "number", "description": "默认图片宽度", "required": false, "default": 2400}, "MINDMAP_HEIGHT": {"type": "number", "description": "默认图片高度", "required": false, "default": 1600}, "MINDMAP_WAIT_TIME": {"type": "number", "description": "渲染等待时间（毫秒）", "required": false, "default": 8000}}, "capabilities": {"systemPromptPlaceholders": [], "invocationCommands": ["{{MindMapGen}}"]}, "dependencies": {"node": ">=16.0.0", "npm_packages": ["axios", "puppeteer"]}, "features": ["AI内容生成", "Markmap渲染", "多样式主题", "自定义尺寸", "图片输出"], "tags": ["思维导图", "AI生成", "可视化", "Markmap", "图片渲染"], "author": "VCPToolBox", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/vcptoolbox"}, "keywords": ["mindmap", "markmap", "ai-generation", "visualization", "diagram"]}