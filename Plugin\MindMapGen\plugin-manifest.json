{"name": "MindMapGen", "displayName": "思维导图生成器", "version": "1.0.0", "description": "基于AI生成Markmap格式的思维导图并渲染为图片。支持多种样式主题，可自定义图片尺寸，适用于知识梳理、项目规划、学习笔记等场景。", "pluginType": "synchronous", "entryPoint": {"script": "MindMapGen.js"}, "communication": {"protocol": "stdio"}, "configSchema": {"OPENAI_API_URL": {"type": "string", "description": "OpenAI API 基础URL", "required": false, "default": "https://api.openai.com"}, "OPENAI_API_KEY": {"type": "string", "description": "OpenAI API 密钥，用于AI生成思维导图内容", "required": true}, "MINDMAP_MODEL": {"type": "string", "description": "用于生成思维导图的AI模型", "required": false, "default": "gpt-4o-mini"}, "MINDMAP_TIMEOUT": {"type": "number", "description": "AI API调用超时时间（毫秒）", "required": false, "default": 30000}, "PROJECT_BASE_PATH": {"type": "string", "description": "项目基础路径，图片将保存到 PROJECT_BASE_PATH/image/mindmapgen/ 目录", "required": false, "default": "../../"}, "MINDMAP_WIDTH": {"type": "number", "description": "默认图片宽度", "required": false, "default": 2400}, "MINDMAP_HEIGHT": {"type": "number", "description": "默认图片高度", "required": false, "default": 1600}, "MINDMAP_WAIT_TIME": {"type": "number", "description": "渲染等待时间（毫秒）", "required": false, "default": 8000}, "DebugMode": {"type": "boolean", "description": "是否启用调试模式", "required": false, "default": false}}, "capabilities": {"systemPromptPlaceholders": [], "invocationCommands": [{"commandIdentifier": "MindMapGen", "description": "调用此工具生成AI思维导图。基于用户描述生成Markmap格式的思维导图并渲染为图片。请在您的回复中，使用以下精确格式来请求，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MindMapGen「末」,\nprompt:「始」思维导图内容描述，详细描述要生成的思维导图主题和结构「末」,\nwidth:「始」(可选, 默认2400) 输出图片宽度（像素）「末」,\nheight:「始」(可选, 默认1600) 输出图片高度（像素）「末」,\nstyle:「始」(可选, 默认default) 样式主题：default、colorful、dark、minimal「末」,\nwaitTime:「始」(可选, 默认8000) 渲染等待时间（毫秒）「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含思维导图的Markdown格式内容，包括图片路径和详细信息。请基于这些结果向用户展示思维导图。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MindMapGen「末」,\nprompt:「始」人工智能的发展历程和应用领域「末」,\nstyle:「始」colorful「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "dependencies": {"node": ">=16.0.0", "npm_packages": ["axios", "puppeteer"]}, "features": ["AI内容生成", "Markmap渲染", "多样式主题", "自定义尺寸", "图片输出"], "tags": ["思维导图", "AI生成", "可视化", "Markmap", "图片渲染"], "author": "VCPToolBox", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/vcptoolbox"}, "keywords": ["mindmap", "markmap", "ai-generation", "visualization", "diagram"]}