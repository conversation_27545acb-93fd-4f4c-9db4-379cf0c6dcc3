@echo off
chcp 65001 >nul
echo 🚀 MindMapGen 插件安装程序
echo.

echo 📋 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Node.js，请先安装Node.js 16.0.0或更高版本
    echo 💡 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
echo.

echo 📦 安装依赖包...
npm install
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装完成
echo.

echo ⚙️ 配置环境文件...
if not exist config.env (
    if exist config.env.example (
        copy config.env.example config.env >nul
        echo ✅ 已创建 config.env 文件
        echo ⚠️  请编辑 config.env 文件，填入您的API密钥
    ) else (
        echo ❌ 找不到 config.env.example 文件
    )
) else (
    echo ℹ️  config.env 文件已存在
)
echo.

echo 📁 创建输出目录...
if not exist "..\..\resources\mindmaps" (
    mkdir "..\..\resources\mindmaps" 2>nul
    echo ✅ 已创建输出目录
) else (
    echo ℹ️  输出目录已存在
)
echo.

echo 🎉 安装完成！
echo.
echo 📋 下一步操作：
echo 1. 编辑 config.env 文件，填入您的OpenAI API密钥
echo 2. 运行测试: node test.js
echo 3. 快速体验: node quick-start.js
echo 4. 在VCP系统中使用: {{MindMapGen}}
echo.
echo 💡 示例用法：
echo {{MindMapGen}}
echo 参数：{"prompt": "人工智能的发展历程和应用领域"}
echo.

pause
