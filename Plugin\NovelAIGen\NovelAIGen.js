#!/usr/bin/env node
const axios = require("axios");
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production'
        ? '../../utils/logger.cjs'
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

// Simple UUID generator (替代uuid库)
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * 统一配置管理器
 */
class ConfigManager {
    constructor() {
        this.config = {};
        this.loadConfig();
    }

    /**
     * 加载配置，优先级：插件配置 > 主服务器环境变量 > 默认值
     */
    loadConfig() {
        // 1. 默认配置
        const defaultConfig = {
            YUANPLUS_API_KEY: '',
            PROJECT_BASE_PATH: '',
            ENABLE_CHARACTER_APPEARANCE: true,
            DEFAULT_CHARACTER_APPEARANCE: 'best quality, ultra-detailed, absurdres, 1girl, anime style'
        };

        // 2. 加载插件配置
        const pluginConfig = this.loadPluginEnvConfig();

        // 3. 配置优先级：插件配置 > 主服务器环境变量 > 默认值
        this.config = {
            // API配置 - 统一使用 YUANPLUS_API_KEY
            YUANPLUS_API_KEY: this.getConfigValue(
                pluginConfig.YUANPLUS_API_KEY,
                process.env.YUANPLUS_API_KEY,
                defaultConfig.YUANPLUS_API_KEY
            ),

            // 项目路径配置
            PROJECT_BASE_PATH: this.getConfigValue(
                pluginConfig.PROJECT_BASE_PATH,
                process.env.PROJECT_BASE_PATH,
                defaultConfig.PROJECT_BASE_PATH
            ),

            // 角色外貌特征相关配置
            ENABLE_CHARACTER_APPEARANCE: this.getConfigValue(
                pluginConfig.ENABLE_CHARACTER_APPEARANCE,
                process.env.ENABLE_CHARACTER_APPEARANCE,
                defaultConfig.ENABLE_CHARACTER_APPEARANCE
            ),

            DEFAULT_CHARACTER_APPEARANCE: this.getConfigValue(
                pluginConfig.DEFAULT_CHARACTER_APPEARANCE,
                process.env.DEFAULT_CHARACTER_APPEARANCE,
                defaultConfig.DEFAULT_CHARACTER_APPEARANCE
            ),

            // 全局角色特征（从主服务器获取）
            CharAppearance: process.env.CharAppearance || '',

            // 角色外貌配置映射
            characterAppearances: this.loadCharacterAppearances(pluginConfig)
        };

        // 验证必需配置
        this.validateConfig();
    }

    /**
     * 获取配置值，按优先级返回第一个非空值
     */
    getConfigValue(pluginValue, envValue, defaultValue) {
        if (pluginValue !== undefined && pluginValue !== '') return pluginValue;
        if (envValue !== undefined && envValue !== '') return envValue;
        return defaultValue;
    }

    /**
     * 加载插件环境配置
     */
    loadPluginEnvConfig() {
        try {
            const pluginConfigPath = path.join(__dirname, 'config.env');
            const configContent = require('fs').readFileSync(pluginConfigPath, 'utf8');
            const config = parseEnvConfig(configContent);
            //logger.debug('NovelAIGen', `从插件config.env加载了 ${Object.keys(config).length} 个配置项`);
            return config;
        } catch (error) {
            //logger.debug('NovelAIGen', '插件config.env不存在或读取失败，使用主服务器配置作为备用');
            return {};
        }
    }

    /**
     * 加载角色外貌配置
     */
    loadCharacterAppearances(pluginConfig) {
        const characterAppearances = {};
        const allConfigs = { ...process.env, ...pluginConfig };

        for (const [key, value] of Object.entries(allConfigs)) {
            if (key.endsWith('_CHARACTER_APPEARANCE') && value) {
                const characterName = key.replace('_CHARACTER_APPEARANCE', '');
                characterAppearances[characterName] = value;
            }
        }

        return characterAppearances;
    }

    /**
     * 验证必需配置
     */
    validateConfig() {
        const requiredConfigs = ['YUANPLUS_API_KEY', 'PROJECT_BASE_PATH'];
        const missingConfigs = requiredConfigs.filter(key => !this.config[key]);

        if (missingConfigs.length > 0) {
            logger.warning('NovelAIGen', `缺少必需配置: ${missingConfigs.join(', ')}`);
        }
    }

    /**
     * 获取配置
     */
    getConfig() {
        return this.config;
    }
}

/**
 * 解析.env格式的配置文件
 */
function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');

                // 处理布尔值和数字
                if (value.toLowerCase() === 'true') {
                    config[key] = true;
                } else if (value.toLowerCase() === 'false') {
                    config[key] = false;
                } else if (!isNaN(value) && value !== '') {
                    config[key] = parseFloat(value);
                } else {
                    config[key] = value;
                }
            }
        }
    });
    return config;
}

// --- Configuration (统一配置管理) ---
const configManager = new ConfigManager();
const config = configManager.getConfig();

const YUANPLUS_API_KEY = config.YUANPLUS_API_KEY;
const PROJECT_BASE_PATH = config.PROJECT_BASE_PATH;

// 角色外貌特征相关配置
const ENABLE_CHARACTER_APPEARANCE = config.ENABLE_CHARACTER_APPEARANCE;
const DEFAULT_CHARACTER_APPEARANCE = config.DEFAULT_CHARACTER_APPEARANCE;
const CHAR_APPEARANCE = config.CharAppearance;
const CHARACTER_APPEARANCES = config.characterAppearances;

// YuanPlus API specific configurations
const YUANPLUS_API_CONFIG = {
    BASE_URL: 'https://yuanplus.cloud/v1/chat/completions',
    DEFAULT_MODEL: 'luminaArchitect-v1-fast',
    AVAILABLE_MODELS: [
        'luminaArchitect-v1-fast',
        'luminaArchitect-v1-turbo',
        'luminaArchitect-v1-plus',
        'noobai-fast',
        'kusa-image-generator',
        'wai-illustrious-free',
        'anishadow-v10-free'
    ]
};

// Helper to validate input arguments
function isValidNovelAIGenArgs(args) {
    if (!args || typeof args !== 'object') return false;
    if (typeof args.prompt !== 'string' || !args.prompt.trim()) return false;
    if (args.model && typeof args.model !== 'string') return false;
    if (args.include_character !== undefined && typeof args.include_character !== 'boolean') return false;
    if (args.character_names !== undefined && !Array.isArray(args.character_names)) return false;
    return true;
}

/**
 * 处理角色外貌的提示词
 * @param {string} scenePrompt - 场景描述
 * @param {boolean} includeCharacter - 是否明确需要画角色
 * @param {string[]} characterNames - 要画的角色名称数组
 * @returns {string} - 处理后的完整提示词
 */
function processCharacterAppearancePrompt(scenePrompt, includeCharacter, characterNames = []) {
    if (!ENABLE_CHARACTER_APPEARANCE || !includeCharacter) {
        return scenePrompt;
    }

    let characterAppearances = [];

    // 遍历角色名称，查找对应的外貌配置
    if (characterNames && characterNames.length > 0) {
        for (const characterName of characterNames) {
            if (CHARACTER_APPEARANCES[characterName]) {
                characterAppearances.push(CHARACTER_APPEARANCES[characterName].trim());
            }
        }
    }

    // 如果没有找到特定角色的外貌配置，使用全局配置或默认配置
    if (characterAppearances.length === 0) {
        if (CHAR_APPEARANCE && CHAR_APPEARANCE.trim()) {
            characterAppearances.push(CHAR_APPEARANCE.trim());
        } else if (DEFAULT_CHARACTER_APPEARANCE && DEFAULT_CHARACTER_APPEARANCE.trim()) {
            characterAppearances.push(DEFAULT_CHARACTER_APPEARANCE.trim());
        }
    }

    // 如果有角色外貌特征，则合并到场景描述中
    if (characterAppearances.length > 0) {
        const combinedAppearances = characterAppearances.join(', ');
        return `${combinedAppearances}, ${scenePrompt}`;
    }

    // 否则直接返回场景描述
    return scenePrompt;
}

async function YTOtherModels(messages, model) {
    try {
        const data = {
            model,
            messages
        };

        const response = await axios.post(YUANPLUS_API_CONFIG.BASE_URL, data, {
            headers: {
                'Authorization': `Bearer ${YUANPLUS_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 180000
        });

        if (response.data.error) {
            console.error(response.data);
            return response.data.error;
        }

        return response.data?.choices?.[0]?.message?.content;
    } catch (error) {
        console.error(error);
        return null;
    }
}

async function generateImageAndSave(args) {
    // Check for essential environment variables
    if (!YUANPLUS_API_KEY) {
        throw new Error("NovelAIGen 插件错误：需要设置 YUANPLUS_API_KEY 环境变量。");
    }
    if (!PROJECT_BASE_PATH) {
        throw new Error("NovelAIGen 插件错误：需要设置 PROJECT_BASE_PATH 环境变量以保存图片。");
    }
    // 不再需要SERVER_PORT，因为使用绝对路径
    // 不再需要IMAGESERVER_IMAGE_KEY、VAR_HTTP_URL，因为使用绝对路径

    if (!isValidNovelAIGenArgs(args)) {
        throw new Error(`NovelAIGen 插件错误：收到无效参数: ${JSON.stringify(args)}。必需参数: prompt (字符串)。可选参数: model (字符串), include_character (布尔值), character_names (字符串数组)。`);
    }

    const model = args.model || YUANPLUS_API_CONFIG.DEFAULT_MODEL;

    // 处理角色外貌的提示词
    const processedPrompt = processCharacterAppearancePrompt(
        args.prompt,
        args.include_character || false,
        args.character_names || []
    );
    const messages = [{ role: "user", content: processedPrompt }];

    // 记录提示词处理信息
    if (processedPrompt !== args.prompt) {
        // logger.info('NovelAIGen', `提示词已处理: "${args.prompt}" -> "${processedPrompt}"`);
        // logger.info('NovelAIGen', `使用角色: ${JSON.stringify(args.character_names || [])}`);
    }

    // Call the image generation API
    const imageUrls = await YTOtherModels(messages, model);

    if (!imageUrls) {
        throw new Error("NovelAIGen 插件错误：无法从 YuanPlus API 获取图片URL。");
    }

    // Parse image URLs (assuming the API returns URLs in the response)
    let imageUrl;
    try {
        // Try to parse as JSON in case the response contains structured data
        const parsed = JSON.parse(imageUrls);
        imageUrl = parsed.image_url || parsed.url || imageUrls;
    } catch (e) {
        // If not JSON, treat as direct URL or search for URL patterns
        const urlMatch = imageUrls.match(/https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp)/i);
        imageUrl = urlMatch ? urlMatch[0] : imageUrls;
    }

    if (!imageUrl || (!imageUrl.startsWith('http') && !imageUrl.startsWith('data:'))) {
        throw new Error("NovelAIGen 插件错误：无法从响应中提取有效的图片URL。");
    }

    let imageData;
    let imageExtension = 'png';

    if (imageUrl.startsWith('data:')) {
        // Handle base64 data URLs
        const matches = imageUrl.match(/^data:image\/([^;]+);base64,(.+)$/);
        if (!matches) {
            throw new Error("NovelAIGen 插件错误：无效的base64图片数据。");
        }
        imageExtension = matches[1];
        imageData = Buffer.from(matches[2], 'base64');
    } else {
        // Download the image from URL
        const imageResponse = await axios({
            method: 'get',
            url: imageUrl,
            responseType: 'arraybuffer',
            timeout: 120000
        });

        imageData = imageResponse.data;
        const contentType = imageResponse.headers['content-type'];
        if (contentType && contentType.startsWith('image/')) {
            imageExtension = contentType.split('/')[1];
        }
    }

    const generatedFileName = `${generateUUID()}.${imageExtension}`;
    const novelAIGenImageDir = path.join(PROJECT_BASE_PATH, 'image', 'novelaigen');
    const localImageServerPath = path.join(novelAIGenImageDir, generatedFileName);

    await fs.mkdir(novelAIGenImageDir, { recursive: true });
    await fs.writeFile(localImageServerPath, imageData);

    // 使用绝对路径而不是服务器URL
    const absoluteImagePath = localImageServerPath;

    // Return markdown format instead of HTML
    const altText = args.prompt ? args.prompt.substring(0, 80) + (args.prompt.length > 80 ? "..." : "") : generatedFileName;
    const successMessage =
        `图片已成功生成！\n\n` +
        `详细信息：\n` +
        `- 使用模型: ${model}\n` +
        `- 图片路径: ${absoluteImagePath}\n` +
        `图片内容：\n` +
        `![${altText}](${absoluteImagePath})\n\n` +
        `请使用上述markdown格式显示图片给用户。`;

    return {
        status: "success",
        message: successMessage,
        image_url: absoluteImagePath,
        image_path: absoluteImagePath,
        model: model,
        prompt: args.prompt,
        include_character: args.include_character || false,
        character_names: args.character_names || [],
        processed_prompt: processedPrompt
    };
}

async function main() {
    let inputChunks = [];
    process.stdin.setEncoding('utf8');

    for await (const chunk of process.stdin) {
        inputChunks.push(chunk);
    }
    const inputData = inputChunks.join('');
    let parsedArgs;

    try {
        if (!inputData.trim()) {
            console.log(JSON.stringify({ status: "error", error: "NovelAIGen 插件错误：未从标准输入接收到输入数据。" }));
            process.exit(1);
        }
        parsedArgs = JSON.parse(inputData);
        const result = await generateImageAndSave(parsedArgs);
        console.log(JSON.stringify(result));
    } catch (e) {
        const errorMessage = e.message || "NovelAIGen 插件未知错误";
        console.log(JSON.stringify({
            status: "error",
            error: errorMessage.startsWith("NovelAIGen 插件错误：") ? errorMessage : `NovelAIGen 插件错误：${errorMessage}`
        }));
        process.exit(1);
    }
}

main(); 