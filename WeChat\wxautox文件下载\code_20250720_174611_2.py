# coding: utf-8
# 需要先安装库: pip install Flask Flask-SocketIO
from flask import <PERSON>lask, render_template
from flask_socketio import SocketIO, emit
import random

# --- 游戏逻辑部分 ---
PLAYERS = ['红', '绿', '黄', '蓝']
NUM_PIECES = 4
BOARD_SIZE = 52
FINISH_POS = 52
START_POSITIONS = {'红': 0, '绿': 13, '黄': 26, '蓝': 39}

class LudoGame:
    def __init__(self):
        self.reset()

    def reset(self):
        # -1: 基地, 0-51: 路上, 52: 终点
        self.positions = {p: [-1] * NUM_PIECES for p in PLAYERS}
        self.current_player_idx = 0
        self.game_over = False
        self.last_roll = 0
        self.winner = None
        print("游戏已重置。")

    def get_state(self):
        return {
            'positions': self.positions,
            'currentPlayer': PLAYERS[self.current_player_idx],
            'lastRoll': self.last_roll,
            'gameOver': self.game_over,
            'winner': self.winner
        }

    def roll_die(self):
        self.last_roll = random.randint(1, 6)
        return self.last_roll

    def get_valid_moves(self, player, roll):
        valid_moves = []
        pieces = self.positions[player]
        for i, pos in enumerate(pieces):
            if (roll == 6 and pos == -1) or (pos != -1 and pos != FINISH_POS):
                valid_moves.append(i)
        return valid_moves

    def move_piece(self, player, piece_idx):
        if player != PLAYERS[self.current_player_idx]:
            return # 不是你的回合
        
        roll = self.last_roll
        pos = self.positions[player][piece_idx]

        if pos == -1: # 从基地出发
            self.positions[player][piece_idx] = 0
        else:
            new_pos = pos + roll
            if new_pos > FINISH_POS: # 防止超出终点
                new_pos = pos 
            elif new_pos == FINISH_POS:
                print(f"{player} 的棋子 {piece_idx+1} 到达终点。")
            self.positions[player][piece_idx] = new_pos

        # 碰撞检测
        if self.positions[player][piece_idx] < FINISH_POS:
            self._check_capture(player, piece_idx)

        # 检查胜利
        if all(p == FINISH_POS for p in self.positions[player]):
            self.game_over = True
            self.winner = player
        
        # 切换玩家
        if roll != 6 and not self.game_over:
            self.current_player_idx = (self.current_player_idx + 1) % len(PLAYERS)
        
        self.last_roll = 0 # 重置骰子点数

    def _check_capture(self, current_player, piece_idx):
        player_start = START_POSITIONS[current_player]
        player_pos = self.positions[current_player][piece_idx]
        board_pos = (player_start + player_pos) % BOARD_SIZE

        for opponent in PLAYERS:
            if opponent == current_player:
                continue
            opponent_start = START_POSITIONS[opponent]
            for i, pos in enumerate(self.positions[opponent]):
                if pos != -1 and pos < FINISH_POS:
                    opponent_board_pos = (opponent_start + pos) % BOARD_SIZE
                    if opponent_board_pos == board_pos:
                        self.positions[opponent][i] = -1 # 送回基地
                        print(f"碰撞！{current_player} 将 {opponent} 的棋子送回基地。")

# --- Flask 服务器部分 ---
app = Flask(__name__)
app.config['SECRET_KEY'] = 'a_very_secret_key' # 生产环境应使用更安全的密钥
socketio = SocketIO(app)
game = LudoGame()

@app.route('/')
def index():
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    print('客户端已连接。')
    emit('update_state', game.get_state())

@socketio.on('roll_dice')
def handle_roll_dice():
    if game.game_over: return
    player = PLAYERS[game.current_player_idx]
    roll = game.roll_die()
    valid_moves = game.get_valid_moves(player, roll)
    
    # 如果没有可移动的棋子，自动切换玩家
    if not valid_moves:
        if roll != 6:
            game.current_player_idx = (game.current_player_idx + 1) % len(PLAYERS)
        game.last_roll = 0
    
    emit('update_state', game.get_state(), broadcast=True)

@socketio.on('move_piece')
def handle_move_piece(data):
    if game.game_over: return
    player = PLAYERS[game.current_player_idx]
    piece_idx = data['pieceIndex']
    game.move_piece(player, piece_idx)
    emit('update_state', game.get_state(), broadcast=True)

@socketio.on('reset_game')
def handle_reset_game():
    game.reset()
    emit('update_state', game.get_state(), broadcast=True)

if __name__ == '__main__':
    socketio.run(app, debug=True)