# coding: utf-8
import random
import time

# --- 常量定义 ---
PLAYERS = ['红方', '绿方', '黄方', '蓝方']
NUM_PIECES_PER_PLAYER = 4
BOARD_SIZE = 52  # 环形棋盘总格子数
FINISH_POSITION = 52 # 完成一圈所需的步数

# 各玩家的起始点
START_POSITIONS = {
    '红方': 0,
    '绿方': 13,
    '黄方': 26,
    '蓝方': 39
}

class LudoGame:
    """
    一个简单的飞行棋游戏逻辑核心。
    """
    def __init__(self):
        # 初始化棋子位置。-1: 在基地; 0-51: 在棋盘上; 52: 到达终点。
        self.piece_positions = {
            player: [-1] * NUM_PIECES_PER_PLAYER for player in PLAYERS
        }
        self.current_player_index = 0
        self.game_over = False
        print("--- 飞行棋游戏已初始化 ---")

    def roll_die(self):
        """模拟投掷骰子。"""
        return random.randint(1, 6)

    def print_board_state(self):
        """打印当前所有棋子的状态，用于调试和展示。"""
        print("\n" + "="*30)
        for player in PLAYERS:
            positions_str = [str(p) if p != -1 else '基地' for p in self.piece_positions[player]]
            print(f"{player} 棋子位置: {', '.join(positions_str)}")
        print("="*30)

    def get_valid_moves(self, player, roll):
        """获取当前玩家在投掷点数下的所有可行动作。"""
        valid_moves = []
        player_pieces = self.piece_positions[player]

        for i, pos in enumerate(player_pieces):
            # 如果投出 6，在基地的棋子可以移动
            if roll == 6 and pos == -1:
                valid_moves.append(i)
            # 在棋盘上的棋子可以移动
            elif pos != -1 and pos != FINISH_POSITION:
                valid_moves.append(i)
        
        return valid_moves

    def move_piece(self, player, piece_index, roll):
        """移动指定的棋子。"""
        current_pos = self.piece_positions[player][piece_index]
        start_pos = START_POSITIONS[player]

        # 从基地出发
        if current_pos == -1:
            self.piece_positions[player][piece_index] = start_pos
            print(f"{player} 的棋子 {piece_index+1} 从基地出发。")
        else:
            # 正常移动
            new_pos = current_pos + roll
            if new_pos >= FINISH_POSITION:
                new_pos = FINISH_POSITION # 到达终点
                print(f"🎉 {player} 的棋子 {piece_index+1} 到达终点！")
            
            self.piece_positions[player][piece_index] = new_pos
            
            # 环形棋盘逻辑，超过51则从0开始
            board_pos = (start_pos + new_pos) % BOARD_SIZE if new_pos < FINISH_POSITION else -100 # 到达终点后不参与碰撞检测

            if board_pos != -100:
                print(f"{player} 的棋子 {piece_index+1} 移动到棋盘格 {board_pos} (总步数: {new_pos})。")
                self._check_capture(player, board_pos)

    def _check_capture(self, current_player, board_pos):
        """检查并处理碰撞，将对方棋子送回基地。"""
        for opponent in PLAYERS:
            if opponent == current_player:
                continue
            
            opponent_start_pos = START_POSITIONS[opponent]
            for i, pos in enumerate(self.piece_positions[opponent]):
                if pos != -1 and pos != FINISH_POSITION:
                    opponent_board_pos = (opponent_start_pos + pos) % BOARD_SIZE
                    if opponent_board_pos == board_pos:
                        self.piece_positions[opponent][i] = -1 # 送回基地
                        print(f"💥 碰撞！{current_player} 将 {opponent} 的棋子 {i+1} 送回基地。")

    def check_win(self, player):
        """检查玩家是否所有棋子都到达终点。"""
        if all(pos == FINISH_POSITION for pos in self.piece_positions[player]):
            self.game_over = True
            print(f"\n🏆🏆🏆 恭喜 {player} 赢得了比赛！ 🏆🏆🏆")

    def next_player(self):
        """切换到下一个玩家。"""
        self.current_player_index = (self.current_player_index + 1) % len(PLAYERS)

    def play_turn(self):
        """执行一个完整的玩家回合。"""
        player = PLAYERS[self.current_player_index]
        print(f"\n--- 现在是 {player} 的回合 ---")
        input("按 Enter 键投掷骰子...")

        roll = self.roll_die()
        print(f"{player} 投出了: {roll}")

        valid_moves = self.get_valid_moves(player, roll)

        if not valid_moves:
            print("没有可以移动的棋子。回合结束。")
            if roll != 6:
                self.next_player()
            else:
                print("投出了6，但无棋可动。获得额外回合。") # 简化规则：投6无棋可走也奖励一次
            return

        # 选择要移动的棋子
        print("你可以移动以下棋子 (输入编号):")
        for piece_index in valid_moves:
            pos = self.piece_positions[player][piece_index]
            pos_str = '基地' if pos == -1 else f'当前在第 {pos} 步'
            print(f"  {piece_index + 1}: {pos_str}")
        
        choice = -1
        while True:
            try:
                choice_input = input("请输入你的选择: ")
                choice = int(choice_input) - 1
                if choice in valid_moves:
                    break
                else:
                    print("无效选择，请重新输入。")
            except ValueError:
                print("输入无效，请输入数字。")

        self.move_piece(player, choice, roll)
        self.check_win(player)

        if self.game_over:
            return

        # 如果投出 6，可以再投一次
        if roll != 6:
            self.next_player()
        else:
            print("投出了 6，你获得一次额外回合。")
            # 不切换玩家，直接进入下一个循环

    def run(self):
        """游戏主循环。"""
        while not self.game_over:
            self.print_board_state()
            self.play_turn()
            time.sleep(0.5) # 暂停一下，让输出更清晰


if __name__ == "__main__":
    game = LudoGame()
    game.run()